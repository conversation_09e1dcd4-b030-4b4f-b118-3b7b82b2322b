-- 创建数据库
CREATE DATABASE IF NOT EXISTS jianli DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE jianli;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
    session_key VARCHAR(100) NOT NULL COMMENT '微信session_key',
    unionid VARCHAR(100) NULL COMMENT '微信unionid',
    nickname VARCHAR(50) NULL COMMENT '昵称',
    avatar_url VARCHAR(500) NULL COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别: 0-未知, 1-男, 2-女',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NULL COMMENT '更新时间',
    INDEX (openid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 创建简历表
CREATE TABLE IF NOT EXISTS resumes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size INT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型(如docx, pdf等)',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '状态: pending-待处理, processing-处理中, completed-已完成, failed-失败',
    upload_time DATETIME NOT NULL COMMENT '上传时间',
    completed_time DATETIME NULL COMMENT '完成时间',
    jd_description TEXT NULL COMMENT 'JD岗位描述',
    analysis_type VARCHAR(20) DEFAULT 'general' COMMENT '分析类型: general-通用分析, technical-技术岗位',
    custom_requirements TEXT NULL COMMENT '自定义分析需求',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX (user_id),
    INDEX (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历表';

-- 创建分析结果表
CREATE TABLE IF NOT EXISTS analysis_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    resume_id INT NOT NULL COMMENT '简历ID',
    target_position VARCHAR(200) NULL COMMENT '目标岗位',
    first_impression TEXT NULL COMMENT '第一印象',
    formatted_text TEXT NULL COMMENT '格式化分析文本',
    analysis_result JSON NULL COMMENT '分析结果JSON',
    revised_resume TEXT NULL COMMENT '修改后的简历范本',
    action_items JSON NULL COMMENT '行动清单',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NULL COMMENT '更新时间',
    FOREIGN KEY (resume_id) REFERENCES resumes(id) ON DELETE CASCADE,
    INDEX (resume_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历分析结果表';

-- 创建简历批次表（用于批量分析）
CREATE TABLE IF NOT EXISTS resume_batches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    batch_name VARCHAR(255) NOT NULL COMMENT '批次名称',
    user_id INT NOT NULL COMMENT '用户ID',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '状态: pending-待处理, processing-处理中, completed-已完成, failed-失败',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历批次表';

-- 创建批次简历关联表
CREATE TABLE IF NOT EXISTS batch_resumes (
    batch_id INT NOT NULL COMMENT '批次ID',
    resume_id INT NOT NULL COMMENT '简历ID',
    order_index INT NOT NULL COMMENT '排序索引',
    PRIMARY KEY (batch_id, resume_id),
    FOREIGN KEY (batch_id) REFERENCES resume_batches(id) ON DELETE CASCADE,
    FOREIGN KEY (resume_id) REFERENCES resumes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批次简历关联表';