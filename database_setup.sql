-- 简历分析系统数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS resume_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE resume_analysis;

-- 创建简历分析结果表
CREATE TABLE IF NOT EXISTS resume_analysis_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
    has_jd TINYINT(1) DEFAULT 0 COMMENT '是否包含JD描述',
    raw_response LONGTEXT COMMENT 'AI原始响应',
    formatted_text LONGTEXT COMMENT '格式化文本',
    analysis_data JSON COMMENT '分析数据JSON',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    ip_address VARCHAR(45) COMMENT '客户端IP地址',
    INDEX idx_created_at (created_at),
    INDEX idx_file_type (file_type),
    INDEX idx_has_jd (has_jd)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历分析结果表';

-- 创建用户会话表（可选，用于跟踪用户使用情况）
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(128) NOT NULL UNIQUE COMMENT '会话ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    first_access TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次访问时间',
    last_access TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后访问时间',
    analysis_count INT DEFAULT 0 COMMENT '分析次数',
    INDEX idx_session_id (session_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_last_access (last_access)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 创建分析统计表（可选，用于统计分析）
CREATE TABLE IF NOT EXISTS analysis_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date_key DATE NOT NULL COMMENT '日期',
    total_analyses INT DEFAULT 0 COMMENT '总分析次数',
    unique_users INT DEFAULT 0 COMMENT '独立用户数',
    file_types JSON COMMENT '文件类型统计',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_date (date_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析统计表';

-- 插入示例数据（可选）
-- INSERT INTO resume_analysis_results (file_name, file_type, has_jd, raw_response, formatted_text, analysis_data, ip_address) 
-- VALUES ('test_resume.pdf', 'application/pdf', 0, 'Test response', 'Test formatted text', '{"test": "data"}', '127.0.0.1');

-- 查询语句示例
-- 查看最近的分析结果
-- SELECT id, file_name, file_type, has_jd, created_at FROM resume_analysis_results ORDER BY created_at DESC LIMIT 10;

-- 统计分析次数
-- SELECT COUNT(*) as total_count, DATE(created_at) as analysis_date FROM resume_analysis_results GROUP BY DATE(created_at) ORDER BY analysis_date DESC;

-- 统计文件类型分布
-- SELECT file_type, COUNT(*) as count FROM resume_analysis_results GROUP BY file_type ORDER BY count DESC;

-- 查看包含JD的分析
-- SELECT id, file_name, created_at FROM resume_analysis_results WHERE has_jd = 1 ORDER BY created_at DESC;
