<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown渲染测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            border: 2px solid #f0f0f0;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-content {
            background: #f8f8f8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .rendered-content {
            border: 2px solid #f0f0f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        /* 模拟小程序样式 */
        .md-title {
            font-weight: bold;
            margin: 30px 0 20px 0;
            color: #333333;
            line-height: 1.4;
        }
        .md-title-1 {
            font-size: 24px;
            border-bottom: 3px solid #333333;
            padding-bottom: 10px;
        }
        .md-title-2 {
            font-size: 20px;
            border-bottom: 2px solid #666666;
            padding-bottom: 8px;
        }
        .md-title-3 {
            font-size: 18px;
            border-bottom: 1px solid #999999;
            padding-bottom: 6px;
        }
        .md-paragraph {
            margin: 16px 0;
            line-height: 1.8;
            color: #333333;
            font-size: 16px;
        }
        .md-bold {
            font-weight: bold;
            color: #000000;
        }
        .md-list-item {
            display: flex;
            margin: 12px 0;
            line-height: 1.8;
            color: #333333;
            font-size: 16px;
        }
        .md-bullet {
            color: #666666;
            margin-right: 8px;
        }
        .md-break {
            height: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Markdown渲染效果测试</h1>
        
        <h2>测试用的Markdown内容：</h2>
        <div class="test-content" id="testMarkdown">**【洞察人心的面试官与资深HRBP】简历审计报告**

**Step 1: 第一印象与初步诊断 (First Impression & Initial Diagnosis)**

1. 目标定位判断: 综合简历内容（技术工作经历明确清晰），以及您提供的JD要求高级能力，我认为这份简历的目标定位是：**高级技术专家**（考虑到2014年开始工作，到2025年已有11年工作经验），然而，15K的期望薪资与11年经验在FAANG级别公司中严重不符，更符合初中级岗位或者非一线城市的薪资水平。

* 职业故事线 (Career Narrative):
* 批判: 简历呈现出清晰的职业路径发展轨迹，从初级开发到高级开发，再到P2P外包公司的项目管理（含创业），最后到房产品牌建设项目负责。这种跨度较大的履历轨迹，尤其是P2P外包公司经历，可能会让面试官产生疑虑（P2P外包公司，以及丹麦外包项目经验及其管理系统，需要进一步的项目可信度验证）。

🔍 建议
1. 在个人简历中，主动且重要的信息设计专业客户品牌理念和策略，例如：优质用户产品体验的管理和开发能力，或攻克用户产品经理。

* [] 软技能的完整性 & 影响力的证明:
* 批判: 客户品牌经验，团队协作经验，项目跟踪经验，产品代理经验，数据分析能力，完全缺失。

🔍 建议: 制作综合能力证明体系，例如：为团队用户产品代理[XX营销策略]，在[X个月内]产品用户增长[Y%]。</div>

        <h2>渲染效果预览：</h2>
        <div class="rendered-content" id="renderedContent">
            <!-- 这里将显示渲染后的内容 -->
        </div>

        <button onclick="testMarkdownParsing()" style="background: #333; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; margin: 20px 0;">测试Markdown解析</button>
    </div>

    <script>
        // 清理文本中的多余符号
        function cleanText(text) {
            return text
                .replace(/^\*\*\s*/, '') // 移除行首的 "** "
                .replace(/\s*\*\*$/, '') // 移除行尾的 " **"
                .replace(/^\*\s+/, '') // 移除行首的 "* "
                .replace(/^\[\]\s*/, '') // 移除行首的 "[] "
                .replace(/^[-•]\s+/, '') // 移除行首的 "- " 或 "• "
                .replace(/^【/, '') // 移除行首的 "【"
                .replace(/】$/, '') // 移除行尾的 "】"
                .trim();
        }

        // 模拟小程序的Markdown解析函数
        function parseMarkdown(text) {
            if (!text) return [];

            const lines = text.split('\n');
            const parsedContent = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];

                // 解析标题 (# ## ###)
                if (line.match(/^#{1,3}\s+/)) {
                    const level = line.match(/^#+/)[0].length;
                    let title = line.replace(/^#+\s+/, '');
                    // 移除标题中的加粗符号
                    title = title.replace(/\*\*(.*?)\*\*/g, '$1');
                    parsedContent.push({
                        type: 'title',
                        level: level,
                        content: title
                    });
                }
                // 解析加粗文本 (**text**)
                else if (line.includes('**')) {
                    const parts = parseInlineMarkdown(line);
                    parsedContent.push({
                        type: 'paragraph',
                        parts: parts
                    });
                }
                // 解析列表项 (- 或 * 或 数字. 或 emoji符号)
                else if (line.match(/^[\s]*[-*•]\s+/) || line.match(/^[\s]*\d+\.\s+/) || line.match(/^[\s]*[🔍💡📋🎯]\s*/)) {
                    let content = line
                        .replace(/^[\s]*[-*•]\s+/, '') // 移除 - * • 符号
                        .replace(/^[\s]*\d+\.\s+/, '') // 移除数字编号
                        .replace(/^[\s]*[🔍💡📋🎯]\s*/, ''); // 移除emoji符号
                    const parts = parseInlineMarkdown(content);
                    parsedContent.push({
                        type: 'list',
                        parts: parts
                    });
                }
                // 普通段落
                else if (line.trim()) {
                    // 先清理文本，再解析
                    const cleanedLine = cleanText(line);
                    if (cleanedLine) { // 确保清理后还有内容
                        const parts = parseInlineMarkdown(cleanedLine);
                        parsedContent.push({
                            type: 'paragraph',
                            parts: parts
                        });
                    }
                }
                // 空行
                else {
                    parsedContent.push({
                        type: 'break'
                    });
                }
            }
            
            return parsedContent;
        }

        // 解析行内Markdown
        function parseInlineMarkdown(text) {
            const parts = [];

            // 使用统一的清理函数
            let cleanedText = cleanText(text);

            const boldRegex = /\*\*(.*?)\*\*/g;
            let lastIndex = 0;
            let match;
            
            while ((match = boldRegex.exec(cleanedText)) !== null) {
                if (match.index > lastIndex) {
                    const normalText = cleanedText.substring(lastIndex, match.index);
                    if (normalText) {
                        parts.push({
                            type: 'text',
                            content: normalText
                        });
                    }
                }

                parts.push({
                    type: 'bold',
                    content: match[1]
                });

                lastIndex = match.index + match[0].length;
            }

            if (lastIndex < cleanedText.length) {
                const remainingText = cleanedText.substring(lastIndex);
                if (remainingText) {
                    parts.push({
                        type: 'text',
                        content: remainingText
                    });
                }
            }

            if (parts.length === 0) {
                parts.push({
                    type: 'text',
                    content: cleanedText
                });
            }
            
            return parts;
        }

        // 渲染Markdown内容
        function renderMarkdown(parsedContent) {
            let html = '';
            
            parsedContent.forEach(item => {
                if (item.type === 'title') {
                    html += `<div class="md-title md-title-${item.level}">${item.content}</div>`;
                } else if (item.type === 'paragraph') {
                    html += '<div class="md-paragraph">';
                    item.parts.forEach(part => {
                        if (part.type === 'bold') {
                            html += `<span class="md-bold">${part.content}</span>`;
                        } else {
                            html += `<span>${part.content}</span>`;
                        }
                    });
                    html += '</div>';
                } else if (item.type === 'list') {
                    html += '<div class="md-list-item">';
                    html += '<span class="md-bullet">• </span>';
                    item.parts.forEach(part => {
                        if (part.type === 'bold') {
                            html += `<span class="md-bold">${part.content}</span>`;
                        } else {
                            html += `<span>${part.content}</span>`;
                        }
                    });
                    html += '</div>';
                } else if (item.type === 'break') {
                    html += '<div class="md-break"></div>';
                }
            });
            
            return html;
        }

        function testMarkdownParsing() {
            const testText = document.getElementById('testMarkdown').textContent;
            const parsed = parseMarkdown(testText);
            const rendered = renderMarkdown(parsed);
            
            document.getElementById('renderedContent').innerHTML = rendered;
            
            console.log('解析结果:', parsed);
        }

        // 页面加载时自动测试
        window.onload = function() {
            testMarkdownParsing();
        };
    </script>
</body>
</html>
