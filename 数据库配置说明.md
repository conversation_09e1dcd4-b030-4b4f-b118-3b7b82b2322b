# 简历分析系统 - 数据库配置说明

## 问题分析

**问题**: 虽然流程可以走通，但是数据库中并未存储成功gemini返回的数据

**原因**: PHP文件中缺少数据库存储逻辑

## 解决方案

### 1. ✅ 添加数据库存储功能

#### 数据库配置
在 `jianli.php` 中添加了数据库配置：
```php
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'resume_analysis',
    'charset' => 'utf8mb4'
];
```

#### 数据库连接函数
```php
function getDbConnection($config) {
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        writeLog("数据库连接失败: " . $e->getMessage(), 'ERROR');
        return null;
    }
}
```

#### 数据保存函数
```php
function saveAnalysisResult($pdo, $data) {
    // 保存分析结果到数据库
    // 包含文件信息、分析结果、时间戳等
}
```

### 2. ✅ 数据库表结构

#### 主表：resume_analysis_results
```sql
CREATE TABLE resume_analysis_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,           -- 文件名
    file_type VARCHAR(100) NOT NULL,           -- 文件类型
    has_jd TINYINT(1) DEFAULT 0,              -- 是否包含JD
    raw_response LONGTEXT,                     -- AI原始响应
    formatted_text LONGTEXT,                  -- 格式化文本
    analysis_data JSON,                       -- 分析数据JSON
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45)                    -- 客户端IP
);
```

#### 辅助表：user_sessions（可选）
```sql
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(128) NOT NULL UNIQUE,
    ip_address VARCHAR(45),
    analysis_count INT DEFAULT 0,
    first_access TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_access TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. ✅ 存储流程

#### 数据流程
1. **接收请求** → 处理文件上传
2. **调用Gemini API** → 获取分析结果
3. **解析响应** → 格式化数据
4. **保存到数据库** → 记录完整信息
5. **返回结果** → 包含保存ID

#### 存储的数据
- ✅ **文件信息**: 文件名、类型、是否包含JD
- ✅ **AI响应**: 原始响应和格式化文本
- ✅ **分析数据**: 解析后的结构化数据
- ✅ **元数据**: 时间戳、IP地址等

### 4. ✅ 错误处理

#### 容错机制
- **数据库连接失败**: 记录日志，但不影响API响应
- **保存失败**: 记录警告，继续返回分析结果
- **数据完整性**: 使用事务确保数据一致性

#### 日志记录
```php
writeLog("分析结果已保存到数据库，ID: " . $savedId);
writeLog("保存到数据库失败，但继续返回结果", 'WARN');
writeLog("数据库连接失败，无法保存结果", 'WARN');
```

## 配置步骤

### 1. 数据库设置

#### 创建数据库
```bash
# 登录MySQL
mysql -u root -p

# 执行SQL文件
source database_setup.sql
```

#### 或手动创建
```sql
CREATE DATABASE resume_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE resume_analysis;
-- 然后执行表创建语句
```

### 2. 修改配置

#### 更新数据库配置
在 `jianli.php` 中修改数据库配置：
```php
$db_config = [
    'host' => 'your_host',        // 数据库主机
    'username' => 'your_user',    // 数据库用户名
    'password' => 'your_pass',    // 数据库密码
    'database' => 'resume_analysis',
    'charset' => 'utf8mb4'
];
```

### 3. 权限设置

#### 数据库用户权限
```sql
-- 创建专用用户（推荐）
CREATE USER 'resume_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE ON resume_analysis.* TO 'resume_user'@'localhost';
FLUSH PRIVILEGES;
```

## 验证方法

### 1. 检查数据库连接
```php
// 在jianli.php中添加测试代码
$pdo = getDbConnection($db_config);
if ($pdo) {
    echo "数据库连接成功\n";
} else {
    echo "数据库连接失败\n";
}
```

### 2. 查看保存的数据
```sql
-- 查看最近的分析记录
SELECT id, file_name, file_type, has_jd, created_at 
FROM resume_analysis_results 
ORDER BY created_at DESC 
LIMIT 10;

-- 查看详细的分析数据
SELECT id, file_name, analysis_data 
FROM resume_analysis_results 
WHERE id = 1;
```

### 3. 检查日志
```bash
# 查看API日志
tail -f jianli_api.log

# 查找数据库相关日志
grep "数据库\|保存" jianli_api.log
```

## 数据统计

### 分析统计查询
```sql
-- 每日分析次数
SELECT DATE(created_at) as date, COUNT(*) as count 
FROM resume_analysis_results 
GROUP BY DATE(created_at) 
ORDER BY date DESC;

-- 文件类型分布
SELECT file_type, COUNT(*) as count 
FROM resume_analysis_results 
GROUP BY file_type 
ORDER BY count DESC;

-- 包含JD的分析比例
SELECT 
    SUM(has_jd) as with_jd,
    COUNT(*) - SUM(has_jd) as without_jd,
    COUNT(*) as total,
    ROUND(SUM(has_jd) * 100.0 / COUNT(*), 2) as jd_percentage
FROM resume_analysis_results;
```

## 性能优化

### 1. 索引优化
- ✅ `created_at` 索引：用于时间查询
- ✅ `file_type` 索引：用于类型统计
- ✅ `has_jd` 索引：用于JD分析

### 2. 数据清理
```sql
-- 清理30天前的数据（可选）
DELETE FROM resume_analysis_results 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 3. 备份策略
```bash
# 定期备份数据库
mysqldump -u root -p resume_analysis > backup_$(date +%Y%m%d).sql
```

## 安全考虑

### 1. 数据脱敏
- 不存储敏感个人信息
- IP地址可选择性存储
- 定期清理历史数据

### 2. 访问控制
- 使用专用数据库用户
- 限制数据库权限
- 启用SSL连接（生产环境）

### 3. 数据保护
- 定期备份重要数据
- 监控异常访问
- 实施数据保留政策

## 总结

通过添加完整的数据库存储功能，现在系统可以：

- ✅ **完整记录**: 保存所有分析结果和元数据
- ✅ **容错处理**: 数据库问题不影响API功能
- ✅ **统计分析**: 支持使用情况统计和分析
- ✅ **数据追踪**: 可以追踪和审计所有分析记录

这样就解决了数据库存储问题，确保Gemini返回的数据能够成功保存到数据库中。
