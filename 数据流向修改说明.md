# 简历分析系统 - 数据流向修改说明

## 问题背景

用户需要将数据保存到国内小程序后端的数据库，而不是中转API服务器的数据库。

## 解决方案：数据回传机制

### 原始数据流向
```
小程序 → 本地API → 中转API → Gemini → 中转API → 本地API → 小程序
                                    ↓
                              中转API数据库 ❌
```

### 修改后数据流向
```
小程序 → 本地API → 中转API → Gemini → 中转API → 本地API → 小程序
                                    ↓           ↓
                                 回传数据    本地数据库 ✅
```

## 实现细节

### 1. ✅ 小程序端修改

#### 添加回调URL参数
在 `pages/upload/upload.js` 中：
```javascript
formData: {
  analysisType: that.data.analysisType,
  customRequirements: that.data.customRequirements,
  jd: that.data.jdDescription,
  // 添加本地API回调地址
  callback_url: app.globalData.apiBaseUrl + '/api/save_analysis.php'
}
```

**作用**：告诉中转API要将数据回传到哪个地址

### 2. ✅ 本地API修改

#### 传递回调URL (`api/upload.php`)
```php
// 添加回调URL，让中转API知道要将数据回传到哪里
if (isset($_POST['callback_url']) && !empty($_POST['callback_url'])) {
    $post_fields['callback_url'] = $_POST['callback_url'];
    write_log("设置回调URL: " . $_POST['callback_url']);
} else {
    // 使用默认的本地API地址
    $callback_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/save_analysis.php';
    $post_fields['callback_url'] = $callback_url;
    write_log("使用默认回调URL: " . $callback_url);
}
```

**作用**：将回调URL传递给中转API

### 3. ✅ 中转API修改

#### 数据回传逻辑 (`jianli.php`)
```php
// 尝试将数据回传到本地API保存
$local_api_url = $_POST['callback_url'] ?? null;
if ($local_api_url) {
    writeLog("尝试将数据回传到本地API: " . $local_api_url);
    $callback_success = sendDataToLocalAPI($local_api_url, $result);
    if ($callback_success) {
        writeLog("数据已成功回传到本地API");
        $result['saved_to_local'] = true;
    } else {
        writeLog("回传到本地API失败", 'WARN');
        $result['saved_to_local'] = false;
    }
}
```

#### 回传函数
```php
function sendDataToLocalAPI($callback_url, $data) {
    // 使用CURL将数据POST到本地API
    // 包含完整的分析结果
    // 返回成功/失败状态
}
```

### 4. ✅ 本地数据保存接口

#### 新建 `api/save_analysis.php`
```php
// 接收中转API回传的数据
// 验证数据格式
// 保存到本地数据库
// 返回保存结果
```

#### 数据库表结构
```sql
CREATE TABLE resume_analysis_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    has_jd TINYINT(1) DEFAULT 0,
    raw_response LONGTEXT,
    formatted_text LONGTEXT,
    analysis_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    source VARCHAR(50) DEFAULT 'gemini_api'
);
```

## 配置步骤

### 1. 数据库设置

#### 执行SQL文件
```bash
# 在本地数据库中执行
mysql -u root -p jianli < api/database_setup.sql
```

#### 验证表创建
```sql
USE jianli;
SHOW TABLES;
DESCRIBE resume_analysis_results;
```

### 2. 权限配置

#### 确保本地API可访问
- 检查 `api/save_analysis.php` 文件权限
- 确保Web服务器可以访问该文件
- 测试URL是否可以正常访问

#### 数据库权限
```sql
-- 确保数据库用户有INSERT权限
GRANT SELECT, INSERT ON jianli.resume_analysis_results TO 'root'@'localhost';
```

### 3. 网络配置

#### 确保中转API可以访问本地API
- 如果本地API在内网，需要配置端口转发或使用公网地址
- 确保防火墙允许相关端口访问
- 测试网络连通性

## 数据流程详解

### 1. 请求阶段
```
小程序 → 本地API (upload.php)
参数：file, jd, callback_url

本地API → 中转API (jianli.php)  
参数：file, jd, callback_url
```

### 2. 处理阶段
```
中转API → Gemini API
获取分析结果

中转API → 解析响应
格式化数据
```

### 3. 回传阶段
```
中转API → 本地API (save_analysis.php)
POST数据：{
  action: 'save_analysis_result',
  data: {完整分析结果},
  timestamp: 时间戳,
  source: 'gemini_api'
}

本地API → 本地数据库
保存完整数据
```

### 4. 响应阶段
```
本地API → 中转API
返回：{success: true, saved_id: 123}

中转API → 本地API → 小程序
返回：{...分析结果, saved_to_local: true}
```

## 错误处理

### 1. 网络错误
- **中转API无法访问本地API**：记录警告，继续返回分析结果
- **本地API响应超时**：设置5秒连接超时，10秒总超时
- **网络连接失败**：记录错误日志，不影响主流程

### 2. 数据错误
- **JSON解析失败**：记录错误，返回失败状态
- **数据库保存失败**：记录错误，返回详细错误信息
- **参数验证失败**：返回400错误和具体错误信息

### 3. 容错机制
- **回传失败不影响主功能**：即使数据保存失败，用户仍能看到分析结果
- **详细日志记录**：所有操作都有详细日志，便于排查问题
- **状态标识**：返回结果中包含 `saved_to_local` 标识保存状态

## 验证方法

### 1. 功能测试
```bash
# 1. 上传简历文件
# 2. 查看日志确认回传
tail -f api/logs/$(date +%Y-%m-%d).log

# 3. 检查数据库
mysql -u root -p jianli -e "SELECT * FROM resume_analysis_results ORDER BY created_at DESC LIMIT 5;"
```

### 2. 网络测试
```bash
# 测试本地API可访问性
curl -X POST http://localhost/api/save_analysis.php \
  -H "Content-Type: application/json" \
  -d '{"action":"save_analysis_result","data":{"test":"data"}}'
```

### 3. 日志检查
```bash
# 查看中转API日志
grep "回传" jianli_api.log

# 查看本地API日志  
grep "保存" api/logs/$(date +%Y-%m-%d).log
```

## 优势

### 1. 数据本地化
- ✅ 所有分析数据保存在国内服务器
- ✅ 符合数据安全和隐私要求
- ✅ 便于数据统计和分析

### 2. 系统稳定性
- ✅ 回传失败不影响主功能
- ✅ 详细的错误处理和日志记录
- ✅ 保持原有功能完整性

### 3. 扩展性
- ✅ 支持多种数据来源标识
- ✅ 便于后续功能扩展
- ✅ 支持数据统计和分析

## 总结

通过实现数据回传机制，成功将简历分析数据的存储从中转API服务器转移到国内本地数据库，同时保持了系统的稳定性和功能完整性。这个方案既满足了数据本地化的需求，又保证了用户体验不受影响。
