<?php
/**
 * 合同审查助手配置文件
 */

// 调试模式
define('DEBUG', true);

// 中转API地址
define('API_URL', 'https://www.furrywoo.com/jianli/jianli.php');

// 文件上传配置
define('MAX_FILE_SIZE', 100 * 1024 * 1024); // 100MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);

// 日志配置
define('LOG_FILE', __DIR__ . '/contract_api_log.txt');

// 数据库配置
define('DB_HOST', '127.0.0.1');
define('DB_NAME', 'jianli');
define('DB_USER', 'root');
define('DB_PASS', '59b93187ca17e677');

// 微信小程序配置
define('WX_APPID', 'wx7091fdfa0f647e4b'); // 请修改为您的微信小程序AppID
define('WX_SECRET', '70eaaf8f158413deba28dda1a430c0e6'); // 请修改为您的微信小程序AppSecret

// JWT密钥
define('JWT_SECRET', 'hGc8OiXm5r0PHjlTWbEqZeJv2UNoVd'); // 请修改为随机字符串

// 注意：Gemini API配置已移至中转API服务器，本地不再需要配置

// 日志配置
define('LOG_PATH', __DIR__ . '/logs/');
define('DEBUG_MODE', true);

// 功能开关
define('ENABLE_MOCK_USER', true); // 是否启用模拟用户（用于测试）

// 确保日志目录存在
if (!file_exists(LOG_PATH)) {
    mkdir(LOG_PATH, 0755, true);
}

/**
 * 写入日志
 * 
 * @param string $message 日志内容
 * @param string $level 日志级别
 * @return void
 */
function write_log($message, $level = 'INFO') {
    if ($level == 'DEBUG' && !DEBUG_MODE) {
        return;
    }
    
    $log_file = LOG_PATH . date('Y-m-d') . '.log';
    $time = date('Y-m-d H:i:s');
    $log_message = "[$time][$level] $message" . PHP_EOL;
    
    file_put_contents($log_file, $log_message, FILE_APPEND);
}

/**
 * 输出JSON响应
 * 
 * @param mixed $data 响应数据
 * @param int $status HTTP状态码
 * @return void
 */
function json_response($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// 初始化设置
ini_set('upload_max_filesize', '100M');
ini_set('post_max_size', '100M');
ini_set('memory_limit', '256M');
ini_set('max_execution_time', '300'); 