-- 简历分析系统 - 简化版数据库表结构
-- 兼容MySQL 5.6及以上版本
-- 使用数据库：jianli

USE jianli;

-- 创建简历分析结果表（主表）
CREATE TABLE IF NOT EXISTS resume_analysis_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
    has_jd TINYINT(1) DEFAULT 0 COMMENT '是否包含JD描述',
    raw_response LONGTEXT COMMENT 'AI原始响应',
    formatted_text LONGTEXT COMMENT '格式化文本',
    analysis_data LONGTEXT COMMENT '分析数据JSON字符串',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    ip_address VARCHAR(45) COMMENT '客户端IP地址',
    source VARCHAR(50) DEFAULT 'local' COMMENT '数据来源',
    INDEX idx_created_at (created_at),
    INDEX idx_file_type (file_type),
    INDEX idx_has_jd (has_jd),
    INDEX idx_source (source)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='简历分析结果表';

-- 插入测试数据（可选）
-- INSERT INTO resume_analysis_results (file_name, file_type, has_jd, raw_response, formatted_text, analysis_data, ip_address, source) 
-- VALUES ('test_resume.pdf', 'application/pdf', 0, 'Test response', 'Test formatted text', '{"test": "data"}', '127.0.0.1', 'gemini_api');

-- 验证表创建
SELECT 'resume_analysis_results表创建成功' as status;

-- 查看表结构
DESCRIBE resume_analysis_results;
