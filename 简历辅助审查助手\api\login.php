<?php
/**
 * 微信登录API
 */

// 引入配置文件
require_once 'config.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '只支持POST请求']);
    exit;
}

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);
if (!$data && !empty($_POST)) {
    $data = $_POST; // 如果不是JSON格式，尝试从POST数组获取
}

// 检查是否有code
if (!isset($data['code']) || empty($data['code'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少微信授权码']);
    exit;
}

$code = $data['code'];

// 实例化认证类
$auth = new Auth();

// 调用微信API获取session信息
$wxSessionResult = $auth->getWxSession($code);

// 检查是否获取成功
if ($wxSessionResult['error']) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '获取微信信息失败: ' . $wxSessionResult['msg'],
        'code' => isset($wxSessionResult['code']) ? $wxSessionResult['code'] : 500
    ]);
    exit;
}

// 获取微信session数据
$wxSessionData = $wxSessionResult['data'];

// 登录或注册用户
$userLoginResult = $auth->loginOrRegisterUser($wxSessionData);

// 返回登录结果
echo json_encode([
    'error' => false,
    'token' => $userLoginResult['token'],
    'user_id' => $userLoginResult['user_id'],
    'is_new_user' => $userLoginResult['is_new_user']
]); 