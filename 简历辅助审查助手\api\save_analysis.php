<?php
/**
 * 保存简历分析结果到本地数据库
 */

require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    write_log("处理OPTIONS预检请求");
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    write_log("错误: 不支持的请求方法 " . $_SERVER['REQUEST_METHOD'], 'ERROR');
    json_response(['error' => 'Method Not Allowed'], 405);
}

write_log("接收到保存分析结果请求");

// 获取POST数据
$input = file_get_contents('php://input');
$requestData = json_decode($input, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    write_log("JSON解析失败: " . json_last_error_msg(), 'ERROR');
    json_response(['error' => 'Invalid JSON data'], 400);
}

// 验证请求数据
if (!isset($requestData['action']) || $requestData['action'] !== 'save_analysis_result') {
    write_log("无效的action参数", 'ERROR');
    json_response(['error' => 'Invalid action'], 400);
}

if (!isset($requestData['data'])) {
    write_log("缺少data参数", 'ERROR');
    json_response(['error' => 'Missing data parameter'], 400);
}

$analysisData = $requestData['data'];
write_log("准备保存分析数据，文件名: " . ($analysisData['file_info']['name'] ?? 'unknown'));

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    write_log("数据库连接成功");
    
    // 准备插入数据
    $sql = "INSERT INTO resume_analysis_results (
        file_name, 
        file_type, 
        has_jd, 
        raw_response, 
        formatted_text, 
        analysis_data, 
        created_at,
        ip_address,
        source
    ) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?, ?)";
    
    $stmt = $pdo->prepare($sql);
    
    // 确保JSON数据正确编码
    $analysisJsonData = json_encode($analysisData['analysis'] ?? [], JSON_UNESCAPED_UNICODE);
    if (json_last_error() !== JSON_ERROR_NONE) {
        write_log("JSON编码失败: " . json_last_error_msg(), 'ERROR');
        $analysisJsonData = '{}'; // 使用空JSON对象作为默认值
    }

    $result = $stmt->execute([
        $analysisData['file_info']['name'] ?? 'unknown',
        $analysisData['file_info']['type'] ?? 'unknown',
        $analysisData['file_info']['has_jd'] ? 1 : 0,
        $analysisData['raw_response'] ?? '',
        $analysisData['formatted_text'] ?? '',
        $analysisJsonData,
        $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'gemini_api'
    ]);
    
    if ($result) {
        $insertId = $pdo->lastInsertId();
        write_log("分析结果已保存到本地数据库，ID: " . $insertId);
        
        json_response([
            'success' => true,
            'saved_id' => $insertId,
            'message' => '数据保存成功'
        ]);
    } else {
        write_log("保存分析结果失败", 'ERROR');
        json_response(['error' => '数据保存失败'], 500);
    }
    
} catch (PDOException $e) {
    write_log("数据库错误: " . $e->getMessage(), 'ERROR');
    json_response(['error' => '数据库错误: ' . $e->getMessage()], 500);
} catch (Exception $e) {
    write_log("保存数据时发生异常: " . $e->getMessage(), 'ERROR');
    json_response(['error' => '服务器内部错误'], 500);
}
?>
