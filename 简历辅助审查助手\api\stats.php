<?php
/**
 * 统计数据API
 * 提供用户上传合同数量和发现风险点的统计数据
 */

// 引入配置文件
require_once 'config.php';

// 只有当这个文件被直接访问时(而不是被包含)，才执行主要逻辑
if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
    // 设置跨域头
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    header('Content-Type: application/json; charset=utf-8');
    
    // 处理预检请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        exit(0);
    }
    
    // 检查是否为GET请求
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        write_log('非法请求方法: ' . $_SERVER['REQUEST_METHOD'], 'ERROR');
        json_response(['success' => false, 'error' => '仅支持GET请求'], 405);
    }
    
    write_log('接收到统计数据请求');
    
    // 获取并返回统计数据
    $stats = get_stats();
    json_response([
        'success' => true,
        'data' => $stats
    ]);
}

/**
 * 获取统计数据
 * 
 * @return array 统计数据
 */
function get_stats() {
    // 定义统计数据文件路径
    $stats_file = __DIR__ . '/stats.json';
    
    // 检查统计数据文件是否存在
    if (!file_exists($stats_file)) {
        write_log('统计数据文件不存在，创建初始数据', 'WARNING');
        
        // 创建初始统计数据
        $initial_stats = [
            'contractCount' => 0,
            'riskCount' => 0,
            'lastUpdated' => date('Y-m-d H:i:s')
        ];
        
        // 写入初始数据到文件
        file_put_contents($stats_file, json_encode($initial_stats, JSON_PRETTY_PRINT));
        return $initial_stats;
    }
    
    // 读取统计数据
    $stats_content = file_get_contents($stats_file);
    if ($stats_content === false) {
        write_log('无法读取统计数据文件', 'ERROR');
        return [
            'contractCount' => 0,
            'riskCount' => 0,
            'lastUpdated' => date('Y-m-d H:i:s')
        ];
    }
    
    // 解析JSON数据
    $stats = json_decode($stats_content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        write_log('解析统计数据失败: ' . json_last_error_msg(), 'ERROR');
        return [
            'contractCount' => 0,
            'riskCount' => 0,
            'lastUpdated' => date('Y-m-d H:i:s')
        ];
    }
    
    return $stats;
}

/**
 * 更新统计数据
 * 用于上传新合同时增加计数
 * 
 * @param int $contract_count 增加的合同数量
 * @param int $risk_count 增加的风险点数量
 * @return bool 更新是否成功
 */
function update_stats($contract_count = 1, $risk_count = 0) {
    $stats_file = __DIR__ . '/stats.json';
    
    // 检查文件是否存在
    if (!file_exists($stats_file)) {
        $stats = [
            'contractCount' => $contract_count,
            'riskCount' => $risk_count,
            'lastUpdated' => date('Y-m-d H:i:s')
        ];
    } else {
        // 读取现有数据
        $stats_content = file_get_contents($stats_file);
        if ($stats_content === false) {
            write_log('无法读取统计数据文件进行更新', 'ERROR');
            return false;
        }
        
        // 解析JSON数据
        $stats = json_decode($stats_content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            write_log('解析统计数据失败: ' . json_last_error_msg(), 'ERROR');
            return false;
        }
        
        // 更新计数
        $stats['contractCount'] += $contract_count;
        $stats['riskCount'] += $risk_count;
        $stats['lastUpdated'] = date('Y-m-d H:i:s');
    }
    
    // 写入更新后的数据
    $result = file_put_contents($stats_file, json_encode($stats, JSON_PRETTY_PRINT));
    
    if ($result === false) {
        write_log('无法写入更新后的统计数据', 'ERROR');
        return false;
    }
    
    write_log('统计数据已更新 - 合同: ' . $stats['contractCount'] . ', 风险点: ' . $stats['riskCount']);
    return true;
} 