<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI简历审查助手 - API测试</h1>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="file">选择简历文件 (PDF/DOC/DOCX/图片):</label>
                <input type="file" id="file" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
            </div>
            
            <div class="form-group">
                <label for="jd">JD岗位描述 (可选):</label>
                <textarea id="jd" name="jd" placeholder="请粘贴您要应聘的岗位描述(JD)，这将帮助AI更精准地分析简历匹配度"></textarea>
            </div>
            
            <div class="form-group">
                <label for="analysisType">分析类型:</label>
                <select id="analysisType" name="analysisType">
                    <option value="general">通用分析</option>
                    <option value="technical">技术岗位</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="customRequirements">特殊要求 (可选):</label>
                <textarea id="customRequirements" name="customRequirements" placeholder="请输入您的特定分析需求，如重点关注某个技能、经验等"></textarea>
            </div>
            
            <button type="submit" id="submitBtn">开始分析简历</button>
        </form>
        
        <div class="progress" id="progress" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const result = document.getElementById('result');
            const progress = document.getElementById('progress');
            const progressBar = document.getElementById('progressBar');
            
            // 获取表单数据
            const formData = new FormData();
            const fileInput = document.getElementById('file');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('请选择一个文件', 'error');
                return;
            }
            
            formData.append('file', file);
            formData.append('jd', document.getElementById('jd').value);
            formData.append('analysisType', document.getElementById('analysisType').value);
            formData.append('customRequirements', document.getElementById('customRequirements').value);
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '分析中...';
            progress.style.display = 'block';
            showResult('正在上传文件并分析，请稍候...', 'loading');
            
            // 模拟进度条
            let progressValue = 0;
            const progressInterval = setInterval(() => {
                progressValue += Math.random() * 15;
                if (progressValue > 90) progressValue = 90;
                progressBar.style.width = progressValue + '%';
            }, 500);
            
            try {
                const response = await fetch('./upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                const responseText = await response.text();
                
                if (response.ok) {
                    try {
                        const jsonResult = JSON.parse(responseText);
                        showResult('✅ 分析成功！\n\n' + JSON.stringify(jsonResult, null, 2), 'success');
                    } catch (e) {
                        showResult('✅ 分析完成！\n\n' + responseText, 'success');
                    }
                } else {
                    showResult('❌ 请求失败 (HTTP ' + response.status + ')\n\n' + responseText, 'error');
                }
                
            } catch (error) {
                clearInterval(progressInterval);
                showResult('❌ 网络错误: ' + error.message, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '开始分析简历';
                setTimeout(() => {
                    progress.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 1000);
            }
        });
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = 'result ' + type;
        }
        
        // 文件选择提示
        document.getElementById('file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                console.log(`选择的文件: ${file.name}, 大小: ${fileSize}MB`);
            }
        });
    </script>
</body>
</html>
