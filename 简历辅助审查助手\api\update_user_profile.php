<?php
/**
 * 更新用户资料API
 */

// 引入配置文件
require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 检查是否是POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '只支持POST请求']);
    exit;
}

// 检查是否有授权头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权信息']);
    exit;
}

// 从授权头获取token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证token
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit;
}

// 获取用户ID
$userId = $payload['sub'];

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);
if (!$data && !empty($_POST)) {
    $data = $_POST; // 如果不是JSON格式，尝试从POST数组获取
}

// 验证必要字段
if (!isset($data['nickName']) || empty($data['nickName'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '昵称不能为空']);
    exit;
}

// 处理用户信息
$nickName = $data['nickName'];
$avatarUrl = isset($data['avatarUrl']) ? $data['avatarUrl'] : '';
$gender = isset($data['gender']) ? intval($data['gender']) : 0;

// 连接数据库
$db = new Database();

// 更新用户资料
try {
    $updatedRows = $db->updateUserProfile($userId, $nickName, $avatarUrl, $gender);
    
    if ($updatedRows > 0) {
        echo json_encode([
            'error' => false,
            'msg' => '用户资料更新成功',
            'data' => [
                'user_id' => $userId,
                'nickname' => $nickName,
                'avatar_url' => $avatarUrl,
                'gender' => $gender
            ]
        ]);
    } else {
        echo json_encode([
            'error' => false,
            'msg' => '未检测到资料变更',
            'data' => [
                'user_id' => $userId
            ]
        ]);
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '更新用户资料失败: ' . $e->getMessage()
    ]);
} 