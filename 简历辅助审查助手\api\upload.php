<?php
/**
 * 文件上传处理API
 * 接收微信小程序上传的文件，转发给中转API
 */

// 引入配置文件
require_once 'config.php';
// 引入统计数据处理函数
require_once 'stats.php';

// 设置跨域头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    write_log('非法请求方法: ' . $_SERVER['REQUEST_METHOD'], 'ERROR');
    json_response(['error' => '仅支持POST请求'], 405);
}

write_log('接收到文件上传请求');

// 处理合并上传参数
$is_merged = isset($_POST['merged']) && $_POST['merged'] === 'true';
$project_id = isset($_POST['projectId']) ? $_POST['projectId'] : null;
$file_index = isset($_POST['fileIndex']) ? (int)$_POST['fileIndex'] : 0;
$total_files = isset($_POST['totalFiles']) ? (int)$_POST['totalFiles'] : 1;

// 如果是合并项目的一部分，记录日志
if ($is_merged && $project_id) {
    write_log("接收到合并项目文件: $file_name, 项目ID: $project_id, 文件索引: $file_index/$total_files");
}

// 检查是否有文件上传
if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    $error_message = '文件上传错误: ';
    
    if (isset($_FILES['file'])) {
        switch ($_FILES['file']['error']) {
            case UPLOAD_ERR_INI_SIZE:
                $error_message .= '文件大小超过限制，最大支持100M';
                break;
            case UPLOAD_ERR_FORM_SIZE:
                $error_message .= '文件大小超过限制，最大支持100M';
                break;
            case UPLOAD_ERR_PARTIAL:
                $error_message .= '文件只有部分被上传';
                break;
            case UPLOAD_ERR_NO_FILE:
                $error_message .= '没有文件被上传';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $error_message .= '找不到临时文件夹';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $error_message .= '文件写入失败';
                break;
            case UPLOAD_ERR_EXTENSION:
                $error_message .= '文件上传被扩展停止';
                break;
            default:
                $error_message .= '未知错误';
        }
    } else {
        $error_message .= '没有文件被上传';
    }
    
    write_log($error_message, 'ERROR');
    json_response(['error' => $error_message], 400);
}

// 获取文件信息
$file_path = $_FILES['file']['tmp_name'];
$file_name = $_FILES['file']['name'];
$file_type = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
$file_size = $_FILES['file']['size'];
$mime_type = $_FILES['file']['type'];

// 记录文件信息
write_log("接收到文件: $file_name, 类型: $file_type, 大小: $file_size 字节, MIME: $mime_type");

// 检查文件扩展名
if (!in_array($file_type, ALLOWED_EXTENSIONS)) {
    write_log("不支持的文件类型: $file_type", 'ERROR');
    json_response(['error' => "不支持的文件类型: $file_type"], 400);
}

// 检查文件大小
if ($file_size > MAX_FILE_SIZE) {
    write_log("文件太大: $file_size 字节", 'ERROR');
    json_response(['error' => '文件太大，最大支持100MB'], 400);
}

// 准备转发给中转API
write_log("准备调用中转API: " . API_URL);

// 创建cURL请求
$ch = curl_init(API_URL);

// 创建POST表单数据
$post_fields = [
    'file' => new CURLFile($file_path, $mime_type, $file_name)
];

// 如果有自定义提示词
if (isset($_POST['prompt']) && !empty($_POST['prompt'])) {
    $post_fields['prompt'] = $_POST['prompt'];
    write_log("使用自定义提示词: " . $_POST['prompt']);
}

// 如果有JD岗位描述
if (isset($_POST['jd']) && !empty($_POST['jd'])) {
    $post_fields['jd'] = $_POST['jd'];
    write_log("使用JD岗位描述: " . substr($_POST['jd'], 0, 100) . "...");
}

// 如果有分析类型参数
if (isset($_POST['analysisType']) && !empty($_POST['analysisType'])) {
    $post_fields['analysisType'] = $_POST['analysisType'];
    write_log("使用分析类型: " . $_POST['analysisType']);
}

// 如果有自定义需求
if (isset($_POST['customRequirements']) && !empty($_POST['customRequirements'])) {
    $post_fields['customRequirements'] = $_POST['customRequirements'];
    write_log("使用自定义需求: " . $_POST['customRequirements']);
}

// 添加回调URL，让中转API知道要将数据回传到哪里
if (isset($_POST['callback_url']) && !empty($_POST['callback_url'])) {
    $post_fields['callback_url'] = $_POST['callback_url'];
    write_log("设置回调URL: " . $_POST['callback_url']);
} else {
    // 如果没有提供回调URL，使用默认的本地API地址
    $callback_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/save_analysis.php';
    $post_fields['callback_url'] = $callback_url;
    write_log("使用默认回调URL: " . $callback_url);
}

// 设置cURL选项
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $post_fields,
    CURLOPT_TIMEOUT => 180, // 3分钟超时
    CURLOPT_FOLLOWLOCATION => true
]);

// 执行cURL请求
$response = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// 关闭cURL
curl_close($ch);

// 检查cURL错误
if ($curl_error) {
    write_log("API调用失败: $curl_error", 'ERROR');
    json_response(['error' => "API调用失败: $curl_error"], 500);
}

// 检查HTTP状态码
if ($http_code !== 200) {
    write_log("API返回错误状态码: $http_code", 'ERROR');
    json_response(['error' => "API返回错误状态码: $http_code"], $http_code);
}

// 解析API响应
$decoded_response = json_decode($response, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    write_log("解析API响应失败: " . json_last_error_msg(), 'ERROR');
    json_response(['error' => "解析API响应失败"], 500);
}

// 文件上传成功后，检查是否为合并项目
if ($is_merged && $project_id) {
    // 添加合并项目标识到返回结果
    $decoded_response['merged'] = true;
    $decoded_response['projectId'] = $project_id;
    $decoded_response['fileIndex'] = $file_index;
    $decoded_response['totalFiles'] = $total_files;
    
    write_log("合并项目文件处理完成: $project_id, 文件: $file_index/$total_files");
}

// 计算风险点数量
$risk_count = 0;
if (isset($decoded_response['parsed_json']) && 
    isset($decoded_response['parsed_json']['合同分析']) && 
    isset($decoded_response['parsed_json']['合同分析']['关键风险点'])) {
    $risk_count = count($decoded_response['parsed_json']['合同分析']['关键风险点']);
    write_log("发现风险点数量: " . $risk_count);
}

// 更新统计数据
update_stats(1, $risk_count);

// 成功返回结果
write_log("API调用成功, 返回结果给客户端");
json_response($decoded_response); 