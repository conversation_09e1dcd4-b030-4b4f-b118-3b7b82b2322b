<?php
/**
 * 用户合同历史API
 * 提供用户上传的合同列表和分析结果
 */

// 引入配置文件
require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 检查是否有授权头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权信息']);
    exit;
}

// 从授权头获取token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证token
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit;
}

// 获取用户ID
$userId = $payload['sub'];

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 处理GET请求 - 获取用户合同列表
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        // 准备查询语句
        $stmt = $conn->prepare("
            SELECT c.id, c.file_name, c.file_size, c.upload_time, c.status, 
                  a.contract_type, a.formatted_text, a.analysis_result
            FROM contracts c
            LEFT JOIN analysis_results a ON c.id = a.contract_id
            WHERE c.user_id = :user_id
            ORDER BY c.upload_time DESC
        ");
        
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        
        $contracts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 处理分析结果
        foreach ($contracts as &$contract) {
            // 确保analysis_result是有效的JSON
            if ($contract['analysis_result']) {
                // 不在这里解析JSON，而是让前端处理，避免大量数据传输
                // 只检查是否是有效的JSON
                $testJson = json_decode($contract['analysis_result']);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    // 如果不是有效的JSON，设为null
                    $contract['analysis_result'] = null;
                }
            }
            
            // 限制formatted_text的长度
            if ($contract['formatted_text'] && strlen($contract['formatted_text']) > 1000) {
                $contract['formatted_text'] = substr($contract['formatted_text'], 0, 1000) . '...';
            }
        }
        
        echo json_encode([
            'error' => false,
            'data' => $contracts
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'error' => true,
            'msg' => '获取合同列表失败: ' . $e->getMessage()
        ]);
    }
}
// 处理POST请求 - 删除合同
else if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    try {
        // 检查是否有合同ID
        if (!isset($_POST['contract_id']) || empty($_POST['contract_id'])) {
            http_response_code(400);
            echo json_encode(['error' => true, 'msg' => '缺少合同ID']);
            exit;
        }
        
        $contractId = $_POST['contract_id'];
        
        // 验证合同所有权
        $checkStmt = $conn->prepare("SELECT id FROM contracts WHERE id = :id AND user_id = :user_id");
        $checkStmt->bindParam(':id', $contractId);
        $checkStmt->bindParam(':user_id', $userId);
        $checkStmt->execute();
        
        if ($checkStmt->rowCount() === 0) {
            http_response_code(403);
            echo json_encode(['error' => true, 'msg' => '无权删除该合同']);
            exit;
        }
        
        // 开始事务
        $conn->beginTransaction();
        
        // 先删除分析结果
        $deleteAnalysisStmt = $conn->prepare("DELETE FROM analysis_results WHERE contract_id = :contract_id");
        $deleteAnalysisStmt->bindParam(':contract_id', $contractId);
        $deleteAnalysisStmt->execute();
        
        // 删除合同记录
        $deleteContractStmt = $conn->prepare("DELETE FROM contracts WHERE id = :id");
        $deleteContractStmt->bindParam(':id', $contractId);
        $deleteContractStmt->execute();
        
        // 提交事务
        $conn->commit();
        
        echo json_encode([
            'error' => false,
            'msg' => '合同删除成功'
        ]);
    } catch (PDOException $e) {
        // 回滚事务
        $conn->rollBack();
        
        http_response_code(500);
        echo json_encode([
            'error' => true,
            'msg' => '删除合同失败: ' . $e->getMessage()
        ]);
    }
}
else {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '不支持的请求方法']);
} 