// app.js
App({
  globalData: {
    uploadedFiles: [],
    analysisResults: [],
    apiBaseUrl: "https://jianli.alidog.cn/", // 后端API地址
    userInfo: null, // 用户信息
    token: null, // 用户登录令牌
    isLoggedIn: false, // 登录状态
    resumes: [] // 用户的简历历史
  },
  
  onLaunch: function() {
    console.log('App launched');
    
    // 检查本地是否有登录信息
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token) {
      this.globalData.token = token;
      if (userInfo) {
        try {
          this.globalData.userInfo = JSON.parse(userInfo);
          this.globalData.isLoggedIn = true;
        } catch (e) {
          console.error('解析用户信息失败', e);
        }
      }
      
      // 验证token有效性
      this.checkTokenValidity(token);
    }
  },
  
  // 登录方法
  login: function(callback) {
    const that = this;
    
    // 调用微信登录接口
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 发送 code 到后台换取 openId, sessionKey, unionId
          wx.request({
            url: that.globalData.apiBaseUrl + 'api/login.php',
            method: 'POST',
            data: {
              code: loginRes.code
            },
            success: (res) => {
              if (res.data && !res.data.error) {
                // 保存登录信息
                const token = res.data.token;
                wx.setStorageSync('token', token);
                that.globalData.token = token;
                that.globalData.isLoggedIn = true;
                
                // 回调通知登录成功
                callback && callback({
                  success: true,
                  isNewUser: res.data.is_new_user
                });
                
                // 获取用户合同历史
                that.fetchUserContracts();
              } else {
                // 登录失败
                console.error('登录失败', res.data);
                callback && callback({
                  success: false,
                  error: res.data.msg || '登录失败'
                });
              }
            },
            fail: (err) => {
              console.error('请求登录接口失败', err);
              callback && callback({
                success: false,
                error: '网络请求失败'
              });
            }
          });
        } else {
          console.error('微信登录失败', loginRes);
          callback && callback({
            success: false,
            error: '微信登录失败'
          });
        }
      },
      fail: (err) => {
        console.error('微信登录API调用失败', err);
        callback && callback({
          success: false,
          error: '微信登录失败'
        });
      }
    });
  },
  
  // 验证token有效性
  checkTokenValidity: function(token) {
    const that = this;
    
    wx.request({
      url: that.globalData.apiBaseUrl + 'api/verify_token.php',
      method: 'GET',
      header: {
        'Authorization': token
      },
      success: (res) => {
        if (res.data && !res.data.error) {
          // token有效，更新用户信息
          that.globalData.userInfo = res.data.data;
          that.globalData.isLoggedIn = true;
          
          // 将用户信息保存到本地
          wx.setStorageSync('userInfo', JSON.stringify(res.data.data));
          
          // 获取用户合同历史
          that.fetchUserContracts();
        } else {
          // token无效，清除登录状态
          console.error('token无效', res.data);
          that.clearLoginState();
        }
      },
      fail: (err) => {
        console.error('验证token失败', err);
        // 网络请求失败，不清除登录状态，保留本地登录信息
      }
    });
  },
  
  // 更新用户信息
  updateUserInfo: function(userInfo, callback) {
    const that = this;
    
    if (!this.globalData.token) {
      callback && callback({
        success: false,
        error: '未登录'
      });
      return;
    }
    
    wx.request({
      url: that.globalData.apiBaseUrl + 'api/update_user_profile.php',
      method: 'POST',
      header: {
        'Authorization': that.globalData.token
      },
      data: userInfo,
      success: (res) => {
        if (res.data && !res.data.error) {
          // 更新成功，更新本地用户信息
          that.globalData.userInfo = {
            ...that.globalData.userInfo,
            ...userInfo
          };
          
          wx.setStorageSync('userInfo', JSON.stringify(that.globalData.userInfo));
          
          callback && callback({
            success: true
          });
        } else {
          console.error('更新用户信息失败', res.data);
          callback && callback({
            success: false,
            error: res.data.msg || '更新用户信息失败'
          });
        }
      },
      fail: (err) => {
        console.error('请求更新用户信息接口失败', err);
        callback && callback({
          success: false,
          error: '网络请求失败'
        });
      }
    });
  },
  
  // 获取用户合同历史
  fetchUserContracts: function() {
    const that = this;
    
    if (!this.globalData.token) {
      console.log('未登录，无法获取合同历史');
      return;
    }
    
    wx.request({
      url: that.globalData.apiBaseUrl + 'api/user_contracts.php',
      method: 'GET',
      header: {
        'Authorization': that.globalData.token
      },
      success: (res) => {
        if (res.data && !res.data.error) {
          // 获取成功，保存合同历史
          that.globalData.contracts = res.data.data || [];
        } else {
          console.error('获取合同历史失败', res.data);
        }
      },
      fail: (err) => {
        console.error('请求获取合同历史接口失败', err);
      }
    });
  },
  
  // 清除登录状态
  clearLoginState: function() {
    this.globalData.token = null;
    this.globalData.userInfo = null;
    this.globalData.isLoggedIn = false;
    this.globalData.contracts = [];
    
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
  },
  
  // 退出登录
  logout: function(callback) {
    this.clearLoginState();
    callback && callback({ success: true });
  }
}) 