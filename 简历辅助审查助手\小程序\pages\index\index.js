// 获取应用实例
const app = getApp();
const utils = require('../../utils/api.js');

Page({
  data: {
    recentHistory: [], // 最近的合同审查记录
  },
  
  onLoad: function() {
    // 加载最近的历史记录
    this.loadRecentHistory();
  },
  
  onShow: function() {
    // 每次页面显示时都刷新历史记录
    this.loadRecentHistory();
  },
  
  // 加载最近的历史记录
  loadRecentHistory: function() {
    const history = app.globalData.history || [];
    // 只显示最近的3条记录
    const recentHistory = history.slice(0, 3);
    this.setData({ recentHistory });
  },
  
  // 获取文件类型图标
  getFileTypeIcon: function(fileType) {
    switch(fileType) {
      case 'pdf':
        return '/images/pdf.png';
      case 'doc':
      case 'docx':
        return '/images/doc.png';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return '/images/image.png';
      default:
        return '/images/file.png';
    }
  },
  
  // 格式化时间
  formatTime: function(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    const diffDays = Math.floor(diff / (24 * 60 * 60 * 1000));
    
    if (diffDays === 0) {
      // 同一天，显示时分
      return '今天 ' + date.getHours().toString().padStart(2, '0') + ':' + date.getMinutes().toString().padStart(2, '0');
    } else if (diffDays === 1) {
      return '昨天 ' + date.getHours().toString().padStart(2, '0') + ':' + date.getMinutes().toString().padStart(2, '0');
    } else if (diffDays < 7) {
      return diffDays + '天前';
    } else {
      // 超过7天，显示日期
      return (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
              date.getDate().toString().padStart(2, '0') + ' ' + 
              date.getHours().toString().padStart(2, '0') + ':' + 
              date.getMinutes().toString().padStart(2, '0');
    }
  },
  
  // 上传图片
  uploadImage: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        const fileName = tempFilePath.substring(tempFilePath.lastIndexOf('/') + 1);
        
        // 记录文件信息到全局，以在上传页面使用
        app.globalData.currentContract = {
          filePath: tempFilePath,
          fileName: fileName,
          fileType: tempFilePath.substring(tempFilePath.lastIndexOf('.') + 1).toLowerCase(),
          fileSize: res.tempFiles[0].size
        };
        
        // 跳转到上传页面
        wx.navigateTo({
          url: '/pages/upload/upload'
        });
      }
    });
  },
  
  // 上传PDF
  uploadPDF: function() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].path;
        const fileName = res.tempFiles[0].name || tempFilePath.substring(tempFilePath.lastIndexOf('/') + 1);
        
        // 记录文件信息到全局，以在上传页面使用
        app.globalData.currentContract = {
          filePath: tempFilePath,
          fileName: fileName,
          fileType: 'pdf',
          fileSize: res.tempFiles[0].size
        };
        
        // 跳转到上传页面
        wx.navigateTo({
          url: '/pages/upload/upload'
        });
      }
    });
  },
  
  // 上传Word文档
  uploadDoc: function() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['doc', 'docx'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].path;
        const fileName = res.tempFiles[0].name || tempFilePath.substring(tempFilePath.lastIndexOf('/') + 1);
        const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        
        // 记录文件信息到全局，以在上传页面使用
        app.globalData.currentContract = {
          filePath: tempFilePath,
          fileName: fileName,
          fileType: fileExt,
          fileSize: res.tempFiles[0].size
        };
        
        // 跳转到上传页面
        wx.navigateTo({
          url: '/pages/upload/upload'
        });
      }
    });
  },
  
  // 查看历史记录项目
  viewHistoryItem: function(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.recentHistory[index];
    
    // 将选中的记录存到全局变量
    app.globalData.currentContract = item;
    
    // 根据状态跳转到不同页面
    if (item.status === 'success') {
      wx.navigateTo({
        url: '/pages/result/result'
      });
    } else {
      // 如果是失败状态，可以选择重新上传
      wx.showModal({
        title: '审查失败',
        content: '该合同审查失败，是否重新上传？',
        confirmText: '重新上传',
        cancelText: '查看详情',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/upload/upload'
            });
          } else if (res.cancel) {
            wx.navigateTo({
              url: '/pages/result/result'
            });
          }
        }
      });
    }
  }
}); 