<view class="container">
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="title">AI简历审查助手</text>
    <text class="subtitle">基于AI技术，智能分析合同风险</text>
  </view>
  
  <view class="main-content">
    <view class="upload-options">
      <view class="upload-option" bindtap="uploadImage">
        <view class="option-icon">
          <image src="/images/image.png" mode="aspectFit"></image>
        </view>
        <view class="option-text">
          <text class="option-title">上传图片</text>
          <text class="option-desc">支持JPG、PNG格式</text>
        </view>
      </view>
      
      <view class="upload-option" bindtap="uploadPDF">
        <view class="option-icon">
          <image src="/images/pdf.png" mode="aspectFit"></image>
        </view>
        <view class="option-text">
          <text class="option-title">上传PDF</text>
          <text class="option-desc">支持PDF文档格式</text>
        </view>
      </view>
      
      <view class="upload-option" bindtap="uploadDoc">
        <view class="option-icon">
          <image src="/images/doc.png" mode="aspectFit"></image>
        </view>
        <view class="option-text">
          <text class="option-title">上传Word文档</text>
          <text class="option-desc">支持DOC、DOCX格式</text>
        </view>
      </view>
    </view>
    
    <view class="recent-activity">
      <view class="section-title">
        <text>最近活动</text>
        <navigator url="/pages/history/history" class="view-all">查看全部</navigator>
      </view>
      
      <view class="recent-list">
        <block wx:if="{{recentHistory.length > 0}}">
          <view class="recent-item" wx:for="{{recentHistory}}" wx:key="timestamp" bindtap="viewHistoryItem" data-index="{{index}}">
            <view class="recent-icon">
              <image src="{{getFileTypeIcon(item.fileType)}}" mode="aspectFit"></image>
            </view>
            <view class="recent-info">
              <text class="recent-filename">{{item.fileName || '未命名合同'}}</text>
              <text class="recent-time">{{formatTime(item.timestamp)}}</text>
            </view>
            <view class="recent-status {{item.status === 'success' ? 'status-success' : 'status-fail'}}">
              {{item.status === 'success' ? '已分析' : '失败'}}
            </view>
          </view>
        </block>
        <view wx:else class="empty-history">
          <text>暂无审查记录</text>
        </view>
      </view>
    </view>
  </view>
  
  <view class="footer">
    <text>Powered by Google Gemini AI</text>
  </view>
</view> 