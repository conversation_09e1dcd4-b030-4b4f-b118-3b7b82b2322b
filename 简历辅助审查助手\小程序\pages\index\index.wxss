/* 主容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 头部 */
.header {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #3498db;
  color: white;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 30rpx;
}

/* 上传选项 */
.upload-options {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
}

.upload-option {
  display: flex;
  padding: 30rpx;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.upload-option:last-child {
  border-bottom: none;
}

.upload-option:active {
  background-color: #f7f7f7;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.option-icon image {
  width: 60rpx;
  height: 60rpx;
}

.option-text {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #333;
}

.option-desc {
  font-size: 24rpx;
  color: #999;
}

/* 最近活动 */
.recent-activity {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 10rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.view-all {
  font-size: 24rpx;
  color: #3498db;
  font-weight: normal;
}

.recent-list {
  padding: 10rpx 0;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 20rpx 10rpx;
  border-bottom: 1px solid #f9f9f9;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.recent-icon image {
  width: 44rpx;
  height: 44rpx;
}

.recent-info {
  flex: 1;
}

.recent-filename {
  display: block;
  font-size: 28rpx;
  margin-bottom: 6rpx;
  color: #333;
  max-width: 440rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-time {
  font-size: 24rpx;
  color: #999;
}

.recent-status {
  padding: 6rpx 16rpx;
  border-radius: 100rpx;
  font-size: 22rpx;
}

.status-success {
  background-color: #e8f6e8;
  color: #27ae60;
}

.status-fail {
  background-color: #fde9e9;
  color: #e74c3c;
}

.empty-history {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 页脚 */
.footer {
  padding: 30rpx 0;
  text-align: center;
  font-size: 24rpx;
  color: #999;
  background-color: #f8f9fa;
} 