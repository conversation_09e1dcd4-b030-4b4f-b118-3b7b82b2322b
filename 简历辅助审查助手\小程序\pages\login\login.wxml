<view class="container">
  <!-- Logo和应用信息 -->
  <view class="logo-section">
    <view class="logo-img">
      <image src="/images/logo.png" mode="aspectFit" class="logo-image"></image>
    </view>
    <view class="app-name">AI合同风险审查助手</view>
    <view class="auth-desc">快速分析合同风险，保障您的权益</view>
  </view>
  
  <!-- 登录按钮 -->
  <view class="btn-container">
    <button class="login-btn" bindtap="handleLogin">
      <image src="/images/weixin.png" mode="aspectFit" class="auth-icon-image"></image>
        <text>微信一键登录</text>
    </button>
    
    <!-- 协议勾选 -->
    <view class="privacy-container">
      <view class="checkbox-wrapper" bindtap="togglePrivacyCheckbox">
        <view class="custom-checkbox {{privacyChecked ? 'checked' : ''}}">
          <view class="checkbox-dot" wx:if="{{privacyChecked}}"></view>
        </view>
      </view>
      <view class="privacy-text">
        登录代表您已阅读并同意<text class="privacy-link" bindtap="navigateToPrivacyPolicy">《用户协议与隐私政策》</text>
      </view>
    </view>
  </view>
  
  <!-- 底部版本信息 -->
  <view class="footer">
    <text>AI合同风险审查助手 v1.0.0</text>
  </view>
  
  <!-- 授权弹窗 -->
  <view class="auth-modal {{showAuthModal ? 'show' : ''}}" bindtap="cancelAuth">
    <view class="auth-modal-content" catchtap="preventClose">
      <view class="auth-modal-header">
        <view class="auth-modal-title">微信授权</view>
      </view>
      <view class="auth-modal-body">
        <view class="modal-desc">AI合同风险审查助手申请获得以下权限：</view>
        
        <view class="auth-item">
          <view class="auth-icon">
            <image src="/images/nicheng.png" mode="aspectFit" class="auth-icon-image"></image>
          </view>
          <view class="auth-text">
            <view class="auth-item-title">获取您的昵称、头像</view>
            <view class="auth-item-desc">用于个性化您的账户</view>
          </view>
        </view>
        
        <view class="auth-item">
          <view class="auth-icon">
            <image src="/images/touxiang.png" mode="aspectFit" class="auth-icon-image"></image>
          </view>
          <view class="auth-text">
            <view class="auth-item-title">获取您的公开信息</view>
            <view class="auth-item-desc">用于提供小程序服务</view>
          </view>
        </view>
      </view>
      <view class="auth-modal-footer">
        <view class="auth-btn auth-btn-cancel" bindtap="cancelAuth">拒绝</view>
        <view class="auth-btn auth-btn-confirm" bindtap="confirmAuth">允许</view>
      </view>
    </view>
  </view>
  
  <!-- 用户信息弹窗 -->
  <view class="user-info-modal {{showUserInfoModal ? 'show' : ''}}" bindtap="cancelUserInfo">
    <view class="user-info-content" catchtap="preventClose">
      <view class="user-info-header">
        <view class="user-info-title">完善个人资料</view>
      </view>
      <view class="user-info-body">
        <view class="avatar-container">
          <button open-type="chooseAvatar" bindchooseavatar="onChooseAvatar" class="avatar-btn">
            <image class="avatar-img" src="{{tempUserInfo.avatarUrl}}"></image>
            <view class="avatar-text">点击获取头像</view>
          </button>
        </view>
        
        <view class="nickname-container">
          <view class="nickname-label">点击获取昵称</view>
          <input type="nickname" class="nickname-input" placeholder="请输入昵称" bindinput="onInputNickname" value="{{tempUserInfo.nickName}}" bindnicknamereview="onNicknameReview" />
        </view>
      </view>
      <view class="user-info-footer">
        <view class="user-info-btn user-info-btn-cancel" bindtap="skipUserInfo">跳过</view>
        <view class="user-info-btn user-info-btn-confirm" bindtap="saveUserInfo">保存</view>
      </view>
    </view>
  </view>

  <!-- 隐私政策弹窗 -->
  <view class="privacy-policy-modal {{showPrivacyDialog ? 'show' : ''}}" bindtap="closePrivacyDialog">
    <view class="privacy-modal-content" catchtap="preventClose">
      <view class="privacy-modal-header">
        <view class="privacy-modal-title">用户协议与隐私政策</view>
        <view class="privacy-close-btn" bindtap="closePrivacyDialog">
          <text class="icon-close"></text>
        </view>
      </view>
      
      <scroll-view class="privacy-modal-body" scroll-y="true">
        <view class="content-section">
          <view class="section-title">一、服务条款</view>
          <view class="section-text">
            欢迎使用AI合同风险审查助手服务。请您仔细阅读以下全部内容。如果您使用AI合同风险审查助手服务，您的使用行为将被视为对本条款全部内容的认可。
          </view>
        </view>
        
        <view class="content-section">
          <view class="section-title">二、用户注册</view>
          <view class="section-text">
            <view>1. 用户在使用本服务前需要注册一个AI合同风险审查助手账号。</view>
            <view>2. AI合同风险审查助手账号应当使用微信账号绑定注册，请用户使用尚未与AI合同风险审查助手账号绑定的微信账号。</view>
            <view>3. AI合同风险审查助手可以根据用户需求或产品需要对账号注册和绑定的方式进行变更，而无须事先通知用户。</view>
          </view>
        </view>
        
        <view class="content-section">
          <view class="section-title">三、用户个人信息保护</view>
          <view class="section-text">
            <view>1. 保护用户个人信息是AI合同风险审查助手的基本原则之一。</view>
            <view>2. 您在注册账号或使用本服务的过程中，可能需要提供一些必要的信息，例如：为向您提供合同审查服务，需要您上传合同文件等信息。</view>
            <view>3. 未经您同意，我们不会向任何第三方共享您的个人信息，但法律法规规定的情形除外。</view>
            <view>4. 您可通过本服务中的相关功能或通过联系客服等方式随时访问、修改、删除您的个人信息或撤回您的授权。</view>
          </view>
        </view>
        
        <view class="content-section">
          <view class="section-title">四、微信授权说明</view>
          <view class="section-text">
            <view>1. 为提供更便捷的服务，您可以选择使用微信账号登录本服务。</view>
            <view>2. 在您使用微信登录时，我们将获取您的微信昵称、头像等公开信息。</view>
            <view>3. 未经您的同意，我们不会将获取的信息用于提供服务之外的其他用途。</view>
            <view>4. 您可以随时取消微信账号与本服务的关联。</view>
          </view>
        </view>
        
        <view class="content-section">
          <view class="section-title">五、服务变更、中断或终止</view>
          <view class="section-text">
            <view>1. 鉴于网络服务的特殊性，AI合同风险审查助手有权根据实际情况随时变更、中断或终止部分或全部的服务。</view>
            <view>2. 如发生下列任何一种情形，AI合同风险审查助手有权变更、中断或终止向用户提供的服务，而无需对用户或任何第三方承担任何责任：</view>
            <view class="indent">a) 根据法律规定用户应提交真实信息，而用户提供的个人资料不真实、或与注册时情况不一致，或未及时更新；</view>
            <view class="indent">b) 用户违反相关法律法规或本协议的约定；</view>
            <view class="indent">c) 按照法律规定或主管部门的要求；</view>
            <view class="indent">d) 出于安全的原因或其他必要的情形。</view>
          </view>
        </view>
      </scroll-view>
      
      <view class="privacy-modal-footer">
        <view class="privacy-btn privacy-btn-cancel" bindtap="disagreePrivacyPolicy">不同意</view>
        <view class="privacy-btn privacy-btn-agree" bindtap="agreePrivacyPolicy">同意</view>
      </view>
    </view>
  </view>
</view> 