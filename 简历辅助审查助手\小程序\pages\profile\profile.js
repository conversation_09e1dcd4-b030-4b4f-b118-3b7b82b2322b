const app = getApp();

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    contracts: [],
    loading: false
  },

  onLoad: function() {
    console.log('个人中心页面加载');
  },
  
  onShow: function() {
    // 每次页面显示时检查登录状态
    this.checkLoginStatus();
    
    // 如果已登录，获取最新的合同历史
    if (this.data.isLoggedIn) {
      this.fetchContracts();
    }
  },
  
  // 检查登录状态
  checkLoginStatus: function() {
    const isLoggedIn = app.globalData.isLoggedIn;
    const userInfo = app.globalData.userInfo;
    
    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo
    });
    
    console.log('登录状态:', isLoggedIn, userInfo);
    
    if (isLoggedIn) {
      this.setData({
        contracts: app.globalData.contracts || []
      });
    }
  },
  
  // 获取用户合同列表
  fetchContracts: function() {
    this.setData({ loading: true });
    
    const that = this;
    wx.request({
      url: app.globalData.apiBaseUrl + 'api/user_contracts.php',
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (res.data && !res.data.error && res.data.data) {
          // 保存到全局数据
          app.globalData.contracts = res.data.data;
          
          // 更新页面数据
          that.setData({
            contracts: res.data.data,
            loading: false
          });
        } else {
          console.error('获取合同列表失败', res.data);
          that.setData({ loading: false });
          
          wx.showToast({
            title: '获取合同列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求获取合同列表接口失败', err);
        that.setData({ loading: false });
        
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 跳转到登录页面
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },
  
  // 退出登录
  logout: function() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout(() => {
            this.setData({
              userInfo: null,
              isLoggedIn: false,
              contracts: []
            });
            
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            });
          });
        }
      }
    });
  },
  
  // 查看合同详情
  viewContract: function(e) {
    const contractId = e.currentTarget.dataset.id;
    
    // 检查是否已经有分析结果
    const contract = this.data.contracts.find(item => item.id === contractId);
    
    if (contract) {
      // 将合同数据存入全局
      app.globalData.analysisResults = [{
        fileName: contract.file_name,
        result: {
          formatted_text: contract.formatted_text,
          parsed_json: contract.analysis_result ? JSON.parse(contract.analysis_result) : null
        }
      }];
      
      // 跳转到结果页
      wx.navigateTo({
        url: '/pages/result/result'
      });
    } else {
      wx.showToast({
        title: '合同数据不存在',
        icon: 'none'
      });
    }
  },
  
  // 删除合同记录
  deleteContract: function(e) {
    const contractId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '删除合同',
      content: '确定要删除这份合同记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ loading: true });
          
          wx.request({
            url: app.globalData.apiBaseUrl + 'api/delete_contract.php',
            method: 'POST',
            header: {
              'Authorization': app.globalData.token
            },
            data: {
              contract_id: contractId
            },
            success: (res) => {
              if (res.data && !res.data.error) {
                // 删除成功，重新获取合同列表
                this.fetchContracts();
                
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
              } else {
                console.error('删除合同失败', res.data);
                this.setData({ loading: false });
                
                wx.showToast({
                  title: res.data.msg || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              console.error('请求删除合同接口失败', err);
              this.setData({ loading: false });
              
              wx.showToast({
                title: '网络请求失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  }
}); 