<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 未登录状态 -->
  <block wx:if="{{!isLoggedIn}}">
    <view class="login-card">
      <view class="login-icon">
        <image src="/images/profile_default.png" mode="aspectFill"></image>
      </view>
      <view class="login-text">登录查看您的合同分析历史</view>
      <button class="login-btn" bindtap="goToLogin">登录/注册</button>
    </view>
  </block>
  
  <!-- 已登录状态 -->
  <block wx:else>
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatar_url || '/images/profile_default.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="nickname">{{userInfo.nickname || '微信用户'}}</text>
          <text class="join-date">注册时间: {{userInfo.created_at || '未知'}}</text>
        </view>
      </view>
      <view class="logout-btn" bindtap="logout">退出登录</view>
    </view>
    
    <!-- 合同历史列表 -->
    <view class="section-title">我的合同历史</view>
    
    <!-- 加载中 -->
    <view class="loading" wx:if="{{loading}}">
      <image class="loading-icon" src="/images/loading.gif" mode="aspectFit"></image>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 无数据 -->
    <view class="empty-list" wx:elif="{{contracts.length === 0}}">
      <image class="empty-icon" src="/images/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无合同记录</text>
    </view>
    
    <!-- 合同列表 -->
    <view class="contract-list" wx:else>
      <view class="contract-item" wx:for="{{contracts}}" wx:key="id">
        <view class="contract-icon">
          <image src="/images/contract_icon.png" mode="aspectFit"></image>
        </view>
        <view class="contract-info" bindtap="viewContract" data-id="{{item.id}}">
          <view class="contract-name">{{item.file_name}}</view>
          <view class="contract-date">上传时间: {{item.upload_time}}</view>
          <view class="contract-type">合同类型: {{item.contract_type || '未知类型'}}</view>
        </view>
        <view class="delete-btn" bindtap="deleteContract" data-id="{{item.id}}">删除</view>
      </view>
    </view>
  </block>
  
  <!-- 底部版本信息 -->
  <view class="footer">
    <text class="footer-text">AI合同风险审查助手 v1.0.0</text>
  </view>
</view> 