/* pages/profile/profile.wxss */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 20px;
  background-color: #f8f8f8;
}

/* 未登录样式 */
.login-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 40px 20px;
  margin: 60px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.login-icon {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  overflow: hidden;
  margin-bottom: 20px;
  background-color: #f0f0f0;
}

.login-icon image {
  width: 100%;
  height: 100%;
}

.login-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 30px;
}

.login-btn {
  width: 80%;
  height: 44px;
  background-color: #333;
  color: #fff;
  border-radius: 22px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn::after {
  border: none;
}

/* 已登录用户信息 */
.user-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  margin-right: 15px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.join-date {
  font-size: 12px;
  color: #999;
}

.logout-btn {
  padding: 8px 15px;
  background-color: #f5f5f5;
  border-radius: 15px;
  font-size: 14px;
  color: #666;
}

/* 合同历史部分 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 20px 0 15px;
  padding-left: 5px;
  border-left: 3px solid #333;
}

/* 加载中 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* 空列表 */
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 合同列表 */
.contract-list {
  margin-bottom: 20px;
}

.contract-item {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 5px rgba(0,0,0,0.03);
}

.contract-icon {
  width: 40px;
  height: 40px;
  margin-right: 15px;
  flex-shrink: 0;
}

.contract-icon image {
  width: 100%;
  height: 100%;
}

.contract-info {
  flex: 1;
  min-width: 0; /* 防止flex布局下内容溢出 */
}

.contract-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contract-date {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}

.contract-type {
  font-size: 12px;
  color: #666;
}

.delete-btn {
  padding: 8px;
  color: #ff6b6b;
  font-size: 14px;
  flex-shrink: 0;
}

/* 页脚 */
.footer {
  margin-top: auto;
  text-align: center;
  padding: 20px 0 10px;
}

.footer-text {
  font-size: 12px;
  color: #999;
} 