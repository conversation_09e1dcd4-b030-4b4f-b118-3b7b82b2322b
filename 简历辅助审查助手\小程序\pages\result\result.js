const app = getApp();

Page({
  data: {
    results: [],
    processedResults: [], // 预处理后的结果数组
    currentIndex: 0,
    loading: true,
    fileNames: [], // 文件名称数组，用于下拉选择
    showRawResponse: false // 是否显示原始响应（调试用）
  },

  onLoad: function(options) {
    console.log('结果页面加载，接收到的参数:', options);

    // 检查是否是分享进入
    if (options.shareData) {
      try {
        const shareData = JSON.parse(decodeURIComponent(options.shareData));
        console.log('接收到分享数据:', shareData);

        // 处理分享数据
        const processedResult = {
          result: shareData.result,
          fileName: shareData.fileName,
          parsedContent: this.parseMarkdown(shareData.result.formatted_text || '暂无分析内容')
        };

        this.setData({
          processedResults: [processedResult],
          currentIndex: 0,
          loading: false,
          fileNames: [shareData.fileName]
        });

        return;
      } catch (error) {
        console.error('解析分享数据失败:', error);
      }
    }

    console.log('结果页面加载');
    const rawResults = app.globalData.analysisResults;
    
    // 预处理数据，适配简历分析结果
    const processedResults = rawResults.map(item => {
      const result = {
        fileName: item.fileName,
        error: item.result ? item.result.error : item.error,
        formattedText: item.result ? item.result.formatted_text : (item.formattedText || '无分析结果'),
        analysis: {},
        mergedProject: item.mergedProject || false,
        result: item.result || {}
      };

      // 如果有简历分析结果
      if (item.result && item.result.success) {
        console.log('处理简历分析结果:', item.result);

        // 直接使用简历分析结果
        result.result = {
          success: item.result.success,
          raw_response: item.result.raw_response || '',
          formatted_text: item.result.formatted_text || '',
          analysis: item.result.analysis || {},
          file_info: item.result.file_info || {
            name: item.fileName,
            type: 'unknown',
            has_jd: false
          }
        };

        // 为了兼容性，也设置analysis字段
        result.analysis = item.result.analysis || {};

        // 解析Markdown格式
        const formattedText = result.result.formatted_text || result.formattedText || '暂无分析内容';
        result.parsedContent = this.parseMarkdown(formattedText);

        console.log('处理后的简历分析数据:', result.result);
        console.log('解析后的Markdown内容:', result.parsedContent);
      }
      
      // 如果是合并项目，添加相关信息
      if (item.mergedProject && item.mergedResults) {
        result.mergedResults = item.mergedResults;
      }
      
      return result;
    });
    
    // 生成文件名数组，用于下拉选择器
    const fileNames = processedResults.map((item, index) => {
      return {
        name: item.fileName,
        index: index
      };
    });
    
    // 确定初始标签页 - 根据第一个结果的数据内容决定
    let initialTab = 'overview';
    if (processedResults.length > 0) {
      const firstResult = processedResults[0];
      if (firstResult.analysis) {
        // 检查各部分数据是否存在，优先显示有数据的标签
        if (firstResult.analysis.keyInfo && Object.keys(firstResult.analysis.keyInfo).length > 0) {
          initialTab = 'keyInfo'; // 如果存在关键信息，优先展示
        } else if (firstResult.analysis.mainClauses && firstResult.analysis.mainClauses.length > 0) {
          initialTab = 'clauses';
        } else if (firstResult.analysis.keyRisks && firstResult.analysis.keyRisks.length > 0) {
          initialTab = 'risks';
        } else if (firstResult.analysis.suggestedChanges && firstResult.analysis.suggestedChanges.length > 0) {
          initialTab = 'suggestions';
        }
      }
    }
    
    console.log('初始标签页设置为:', initialTab);
    
    this.setData({
      results: rawResults,
      processedResults: processedResults,
      fileNames: fileNames,
      loading: false,
      activeModuleTab: initialTab // 设置初始标签页
    });
    
    console.log('数据处理完成', processedResults);
  },
  
  // 用户点击右上角分享给好友
  onShareAppMessage: function() {
    let title = '合同风险审核报告';
    // 如果有正在查看的合同，将其类型添加到分享标题
    if (this.data.processedResults.length > 0 && 
        this.data.processedResults[this.data.currentIndex].analysis && 
        this.data.processedResults[this.data.currentIndex].analysis.contractType) {
      title = `${this.data.processedResults[this.data.currentIndex].analysis.contractType}分析报告`;
    }
    
    return {
      title: title,
      path: '/pages/upload/upload' // 分享后打开上传页面
    };
  },
  
  // 用户点击右上角分享到朋友圈
  onShareTimeline: function() {
    let title = '合同风险审核报告';
    // 如果有正在查看的合同，将其类型添加到分享标题
    if (this.data.processedResults.length > 0 && 
        this.data.processedResults[this.data.currentIndex].analysis && 
        this.data.processedResults[this.data.currentIndex].analysis.contractType) {
      title = `${this.data.processedResults[this.data.currentIndex].analysis.contractType}分析报告`;
    }
    
    return {
      title: title,
      query: ''
    };
  },
  
  // 处理关键信息对象，将中文键名转为英文键名
  processKeyInfo: function(keyInfo) {
    if (!keyInfo || typeof keyInfo !== 'object') return {};
    
    console.log('开始处理关键信息：', JSON.stringify(keyInfo));
    
    const processedInfo = {};
    
    // 特殊处理：违约金条款
    if (keyInfo['违约金条款']) {
      processedInfo.liquidatedDamages = keyInfo['违约金条款'];
      console.log('找到违约金条款:', keyInfo['违约金条款']);
    } else if (keyInfo['违约金']) {
      processedInfo.liquidatedDamages = keyInfo['违约金'];
      console.log('找到违约金:', keyInfo['违约金']);
    } else if (keyInfo['违约金规定']) {
      processedInfo.liquidatedDamages = keyInfo['违约金规定'];
      console.log('找到违约金规定:', keyInfo['违约金规定']);
    } else if (keyInfo['违约条款']) {
      processedInfo.liquidatedDamages = keyInfo['违约条款'];
      console.log('找到违约条款:', keyInfo['违约条款']);
    }
    
    // 特殊处理：8字段名
    if (keyInfo['8字段名']) {
      const fieldName = keyInfo['8字段名'];
      // 检测字段内容决定映射到哪个英文字段
      if (fieldName.includes('违约')) {
        processedInfo.liquidatedDamages = fieldName;
        console.log('8字段名映射到违约金条款:', fieldName);
      }
    }
    
    // 处理普通字段
    Object.keys(keyInfo).forEach(key => {
      let englishKey = this.convertKeyToEnglish(key);
      
      // 特殊处理数组或对象
      if (Array.isArray(keyInfo[key])) {
        processedInfo[englishKey] = keyInfo[key]; // 保持数组不变
        console.log('处理数组字段:', key, '→', englishKey);
      } else if (typeof keyInfo[key] === 'object' && keyInfo[key] !== null) {
        // 处理嵌套对象，如合同当事人
        const nestedObj = {};
        Object.keys(keyInfo[key]).forEach(subKey => {
          const englishSubKey = this.convertKeyToEnglish(subKey);
          nestedObj[englishSubKey] = keyInfo[key][subKey];
          console.log('处理嵌套对象字段:', key + '.' + subKey, '→', englishKey + '.' + englishSubKey);
        });
        processedInfo[englishKey] = nestedObj;
      } else {
        processedInfo[englishKey] = keyInfo[key];
        console.log('处理普通字段:', key, '→', englishKey, '值:', keyInfo[key]);
      }
    });
    
    console.log('关键信息处理完成：', JSON.stringify(processedInfo));
    return processedInfo;
  },
  
  // 将中文键名转换为英文键名
  convertKeyToEnglish: function(key) {
    const keyMap = {
      '合同签署日期': 'signingDate',
      '合同生效时间': 'effectiveDate',
      '合同到期日期': 'expirationDate',
      '服务/项目周期': 'servicePeriod',
      '合同总金额': 'totalAmount',
      '付款方式': 'paymentMethod',
      '付款时间节点': 'paymentMilestones',
      '违约金条款': 'liquidatedDamages',
      '违约金': 'liquidatedDamages', // 添加替代名称
      '违约条款': 'liquidatedDamages', // 添加替代名称
      '违约金规定': 'liquidatedDamages', // 添加替代名称
      '争议解决方式': 'disputeResolution',
      '合同当事人': 'parties',
      '甲方': 'partyA',
      '乙方': 'partyB',
      '合同标的物': 'subject',
      '保密条款': 'confidentiality',
      '知识产权': 'intellectualProperty',
      '不可抗力': 'forceMajeure',
      '变更解除条件': 'terminationConditions',
      '税费承担': 'taxBearing',
      '验收标准': 'acceptanceCriteria',
      '提前终止条件': 'earlyTermination'
    };
    
    // 输出调试信息，检查字段映射
    console.log('键名映射:', key, '→', keyMap[key] || key);
    
    return keyMap[key] || key;
  },
  
  // 处理数组数据，将中文键名转为英文键名
  processArrayData: function(array) {
    if (!Array.isArray(array)) return [];
    
    return array.map(item => {
      const newItem = {};
      // 遍历对象的所有键
      Object.keys(item).forEach(key => {
        // 根据不同的中文键名映射为英文键名
        if (key === '条款名称') newItem.name = item[key];
        else if (key === '条款内容') newItem.content = item[key];
        else if (key === '风险等级') newItem.riskLevel = item[key];
        else if (key === '风险分析') newItem.riskAnalysis = item[key];
        else if (key === '风险描述') newItem.riskDescription = item[key];
        else if (key === '可能影响') newItem.impact = item[key];
        else if (key === '建议对策') newItem.suggestion = item[key];
        else if (key === '条款位置') newItem.location = item[key];
        else if (key === '原文内容') newItem.originalText = item[key];
        else if (key === '建议修改为') newItem.suggestedText = item[key];
        else if (key === '修改理由') newItem.reason = item[key];
        else newItem[key] = item[key]; // 其他键保持不变
      });
      
      return newItem;
    });
  },

  // 通过下拉框切换当前显示的文件分析结果
  switchResultByPicker: function(e) {
    const index = parseInt(e.detail.value);
    
    // 根据当前选择的文件结果类型选择合适的标签
    let initialTab = 'overview';
    const currentResult = this.data.processedResults[index];
    
    // 如果有结构化数据但没有关键信息，尝试查看其他标签
    if (currentResult.analysis) {
      // 检查各部分数据是否存在，优先显示有数据的标签
      if (currentResult.analysis.keyInfo && Object.keys(currentResult.analysis.keyInfo).length > 0) {
        initialTab = 'keyInfo'; // 如果存在关键信息，优先展示
      } else if (currentResult.analysis.mainClauses && currentResult.analysis.mainClauses.length > 0) {
        initialTab = 'clauses';
      } else if (currentResult.analysis.keyRisks && currentResult.analysis.keyRisks.length > 0) {
        initialTab = 'risks';
      } else if (currentResult.analysis.suggestedChanges && currentResult.analysis.suggestedChanges.length > 0) {
        initialTab = 'suggestions';
      }
    }
    
    console.log('切换到文件索引:', index, '初始标签:', initialTab);
    
    this.setData({
      currentIndex: index,
      activeModuleTab: initialTab
    });
  },
  
  // 复制完整报告
  copyFullReport: function() {
    const currentResult = this.data.processedResults[this.data.currentIndex];
    if (!currentResult) {
      wx.showToast({
        title: '无内容可复制',
        icon: 'none'
      });
      return;
    }

    const fullText = currentResult.result.formatted_text || currentResult.formattedText || '暂无分析内容';

    wx.setClipboardData({
      data: fullText,
      success: function() {
        wx.showToast({
          title: '完整报告已复制',
          icon: 'success'
        });
      },
      fail: function() {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 清理文本中的多余符号
  cleanText: function(text) {
    return text
      .replace(/^\*\*\s*/, '') // 移除行首的 "** "
      .replace(/\s*\*\*$/, '') // 移除行尾的 " **"
      .replace(/^\*\s+/, '') // 移除行首的 "* "
      .replace(/^\[\]\s*/, '') // 移除行首的 "[] "
      .replace(/^[-•]\s+/, '') // 移除行首的 "- " 或 "• "
      .replace(/^【/, '') // 移除行首的 "【"
      .replace(/】$/, '') // 移除行尾的 "】"
      .trim();
  },

  // 简单的Markdown解析函数
  parseMarkdown: function(text) {
    if (!text) return [];

    const lines = text.split('\n');
    const parsedContent = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 解析标题 (# ## ###)
      if (line.match(/^#{1,3}\s+/)) {
        const level = line.match(/^#+/)[0].length;
        let title = line.replace(/^#+\s+/, '');
        // 移除标题中的加粗符号
        title = title.replace(/\*\*(.*?)\*\*/g, '$1');
        parsedContent.push({
          type: 'title',
          level: level,
          content: title
        });
      }
      // 解析加粗文本 (**text**)
      else if (line.includes('**')) {
        const parts = this.parseInlineMarkdown(line);
        parsedContent.push({
          type: 'paragraph',
          parts: parts
        });
      }
      // 解析列表项 (- 或 * 或 数字. 或 emoji符号)
      else if (line.match(/^[\s]*[-*•]\s+/) || line.match(/^[\s]*\d+\.\s+/) || line.match(/^[\s]*[🔍💡📋🎯]\s*/)) {
        let content = line
          .replace(/^[\s]*[-*•]\s+/, '') // 移除 - * • 符号
          .replace(/^[\s]*\d+\.\s+/, '') // 移除数字编号
          .replace(/^[\s]*[🔍💡📋🎯]\s*/, ''); // 移除emoji符号
        const parts = this.parseInlineMarkdown(content);
        parsedContent.push({
          type: 'list',
          parts: parts
        });
      }
      // 普通段落
      else if (line.trim()) {
        // 先清理文本，再解析
        const cleanedLine = this.cleanText(line);
        if (cleanedLine) { // 确保清理后还有内容
          const parts = this.parseInlineMarkdown(cleanedLine);
          parsedContent.push({
            type: 'paragraph',
            parts: parts
          });
        }
      }
      // 空行
      else {
        parsedContent.push({
          type: 'break'
        });
      }
    }

    return parsedContent;
  },

  // 解析行内Markdown (加粗、emoji等)
  parseInlineMarkdown: function(text) {
    const parts = [];

    // 使用统一的清理函数
    let cleanText = this.cleanText(text);

    // 解析加粗文本 **text**
    const boldRegex = /\*\*(.*?)\*\*/g;
    let lastIndex = 0;
    let match;

    while ((match = boldRegex.exec(cleanText)) !== null) {
      // 添加加粗前的普通文本
      if (match.index > lastIndex) {
        const normalText = cleanText.substring(lastIndex, match.index);
        if (normalText) {
          parts.push({
            type: 'text',
            content: normalText
          });
        }
      }

      // 添加加粗文本
      parts.push({
        type: 'bold',
        content: match[1]
      });

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余的普通文本
    if (lastIndex < cleanText.length) {
      const remainingText = cleanText.substring(lastIndex);
      if (remainingText) {
        parts.push({
          type: 'text',
          content: remainingText
        });
      }
    }

    // 如果没有特殊格式，返回普通文本
    if (parts.length === 0) {
      parts.push({
        type: 'text',
        content: cleanText
      });
    }

    return parts;
  },

  // 复制文本功能
  copyText: function(e) {
    const text = e.currentTarget.dataset.text;
    if (!text) {
      wx.showToast({
        title: '无内容可复制',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: text,
      success: function() {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        });
      },
      fail: function() {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 分享给好友
  onShareAppMessage: function(res) {
    const currentResult = this.data.processedResults[this.data.currentIndex];
    if (!currentResult) {
      return {
        title: '简历分析报告',
        path: '/pages/index/index'
      };
    }

    // 生成分享数据
    const shareData = {
      title: '📋 简历分析报告',
      path: `/pages/result/result?shareData=${encodeURIComponent(JSON.stringify({
        result: currentResult.result,
        fileName: currentResult.fileName || '简历文件',
        timestamp: Date.now()
      }))}`
    };

    console.log('分享数据:', shareData);
    return shareData;
  },

  // 分享到朋友圈
  onShareTimeline: function() {
    const currentResult = this.data.processedResults[this.data.currentIndex];
    if (!currentResult) {
      return {
        title: '简历分析报告'
      };
    }

    return {
      title: '📋 AI简历分析报告 - 专业简历优化建议',
      query: `shareData=${encodeURIComponent(JSON.stringify({
        result: currentResult.result,
        fileName: currentResult.fileName || '简历文件',
        timestamp: Date.now()
      }))}`
    };
  },
  
  // 复制文本到剪贴板
  copyText: function(e) {
    const text = e.currentTarget.dataset.text;
    wx.setClipboardData({
      data: text,
      success: function() {
        wx.showToast({
          title: '内容已复制',
          icon: 'success'
        });
      }
    });
  },
  
  // 下载所有结果
  downloadAll: function() {
    wx.showToast({
      title: '微信小程序暂不支持下载',
      icon: 'none'
    });
  }
}); 