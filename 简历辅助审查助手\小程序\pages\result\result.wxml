<!--pages/result/result.wxml-->
<view class="container">
  <view class="main-card">
    <view class="header">简历分析结果</view>
    
    <!-- 加载中状态 -->
    <block wx:if="{{loading}}">
      <view class="loading">
        <view class="loading-icon"></view>
        <view class="loading-text">正在加载分析结果...</view>
      </view>
    </block>
    
    <!-- 无结果状态 -->
    <block wx:elif="{{processedResults.length === 0}}">
      <view class="no-result">
        <view class="no-result-icon"></view>
        <view class="no-result-text">暂无分析结果</view>
      </view>
    </block>
    
    <!-- 有结果状态 -->
    <block wx:else>
      <!-- 文件选择器下拉框 -->
      <view class="file-selector">
        <picker bindchange="switchResultByPicker" value="{{currentIndex}}" range="{{processedResults}}" range-key="fileName">
          <view class="picker-wrapper">
            <view class="picker-label">选择简历文件:</view>
            <view class="picker-value">
              {{processedResults[currentIndex].fileName}}
              <view class="picker-arrow"></view>
            </view>
          </view>
        </picker>
      </view>
      
      <view class="divider"></view>
      
      <!-- 错误状态 -->
      <block wx:if="{{processedResults[currentIndex].error}}">
        <view class="module error">
          <view class="module-icon error-icon"></view>
          <view class="module-content error-message">
            {{processedResults[currentIndex].error}}
          </view>
        </view>
      </block>
      
      <!-- 内容展示区域 - 无错误时显示 -->
      <block wx:else>
        <!-- 简化为单一标题 -->
        <view class="result-header">
          <view class="result-title">📋 完整分析报告</view>
        </view>
        
        <view class="result-scroll-area">
          <!-- 单一完整分析模块 -->
          <view class="module-container">


            <!-- 完整分析报告 -->
            <view class="module">

              <view class="module-content">
                <!-- 如果有错误，显示错误信息 -->
                <view wx:if="{{processedResults[currentIndex].error}}" class="error-message">
                  <view class="error-title">❌ 分析失败</view>
                  <text class="error-text" selectable="true">{{processedResults[currentIndex].error}}</text>
                </view>

                <!-- 显示完整分析内容 - 支持Markdown -->
                <view wx:else class="full-analysis">
                  <!-- Markdown渲染 -->
                  <view class="markdown-content">
                    <block wx:for="{{processedResults[currentIndex].parsedContent}}" wx:key="index">
                      <!-- 标题 -->
                      <view wx:if="{{item.type === 'title'}}" class="md-title md-title-{{item.level}}">
                        {{item.content}}
                      </view>

                      <!-- 段落 -->
                      <view wx:elif="{{item.type === 'paragraph'}}" class="md-paragraph">
                        <text wx:for="{{item.parts}}" wx:key="partIndex"
                              class="{{item.type === 'bold' ? 'md-bold' : 'md-text'}}"
                              selectable="true">{{item.content}}</text>
                      </view>

                      <!-- 列表项 -->
                      <view wx:elif="{{item.type === 'list'}}" class="md-list-item">
                        <text class="md-bullet">• </text>
                        <text wx:for="{{item.parts}}" wx:key="partIndex"
                              class="{{item.type === 'bold' ? 'md-bold' : 'md-text'}}"
                              selectable="true">{{item.content}}</text>
                      </view>

                      <!-- 换行 -->
                      <view wx:elif="{{item.type === 'break'}}" class="md-break"></view>
                    </block>
                  </view>

                  <!-- 备用显示：如果Markdown解析失败，显示原始文本 -->
                  <view wx:if="{{!processedResults[currentIndex].parsedContent || processedResults[currentIndex].parsedContent.length === 0}}" class="fallback-content">
                    <text class="analysis-content" selectable="true">{{processedResults[currentIndex].result.formatted_text || processedResults[currentIndex].formattedText || '暂无分析内容'}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>


        </view>
      </block>
      
      <!-- 当前简历信息 -->
      <view class="current-file-info">
        当前文件: {{currentIndex + 1}}/{{processedResults.length}}
      </view>
    </block>
  </view>
</view>

<!-- 固定在底部的按钮 -->
<view class="fixed-bottom" wx:if="{{!loading && processedResults.length > 0}}">
  <view class="bottom-buttons">
    <button class="btn-primary" open-type="share">分享给好友</button>
    <button class="btn-primary" 
            wx:if="{{processedResults[currentIndex].formattedText}}"
            bindtap="copyText" 
            data-text="{{processedResults[currentIndex].formattedText}}">
      复制
    </button>
  </view>
</view> 