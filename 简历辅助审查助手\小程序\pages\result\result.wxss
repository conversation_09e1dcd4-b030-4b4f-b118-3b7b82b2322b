/* pages/result/result.wxss */
page {
  background-color: #ffffff;
}

.container {
  padding-bottom: 120rpx; /* 为固定底部按钮留出空间 */
  background-color: #ffffff;
}

.main-card {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
}

.header {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

/* 文件选择器下拉框 */
.file-selector {
  margin: 20rpx 0;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid #eee;
}

.picker-wrapper {
  display: flex;
  flex-direction: column;
}

.picker-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
  color: #333;
  font-size: 30rpx;
  position: relative;
}

.picker-arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 3rpx solid #666;
  border-bottom: 3rpx solid #666;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

/* 当前文件信息 */
.current-file-info {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
  margin-top: 30rpx;
  border-top: 1rpx dashed #eee;
}

/* 模块标签导航 */
.module-tabs {
  display: flex;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  border: 1rpx solid #eee;
}

.module-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.module-tab.active {
  color: #333;
  font-weight: 500;
}

.module-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 6rpx;
  background-color: #333;
  border-radius: 6rpx 6rpx 0 0;
}

/* 内容区域 - 跟随主界面滑动 */
.result-scroll-area {
  padding-bottom: 20rpx;
  /* 移除高度限制和内部滚动，让内容跟随主界面滑动 */
}

/* 模块容器 */
.module-container {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 统计摘要 */
.stats-summary {
  display: flex;
  margin: 30rpx 0;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  min-width: 120rpx;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.stat-item.warning .stat-number {
  color: #f56c6c;
}

.stat-item.info .stat-number {
  color: #409eff;
}

/* 加载和无结果状态 */
.loading, .no-result {
  padding: 100rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon, .no-result-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 30rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.loading-icon {
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { opacity: 0.3; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1); }
  100% { opacity: 0.3; transform: scale(0.8); }
}

.loading-text, .no-result-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

/* 模块通用样式 - 黑白简约主题 */
.module {
  margin-bottom: 40rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #ffffff;
  border: 2rpx solid #f0f0f0;
}

.module:last-child {
  margin-bottom: 0;
}

.module-header {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background-color: #f9f9f9;
  border-bottom: 1rpx solid #f0f0f0;
}

.module-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  background-color: #333;
  border-radius: 50%;
}

.module-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.module-content {
  padding: 24rpx;
}

.module-footer {
  padding: 16rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

/* 空内容提示 */
.empty-message {
  text-align: center;
  color: #999;
  padding: 60rpx 0;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  background: #f2f2f2;
  border-radius: 50%;
  position: relative;
}

.empty-icon::before,
.empty-icon::after {
  content: '';
  position: absolute;
  background: #ccc;
}

.empty-icon::before {
  width: 40rpx;
  height: 6rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-tips {
  font-size: 24rpx;
  color: #999;
  max-width: 80%;
  line-height: 1.5;
}

/* 徽章 */
.badge {
  padding: 6rpx 14rpx;
  background-color: #e8e8e8;
  color: #666;
  border-radius: 20rpx;
  font-size: 22rpx;
  min-width: 22rpx;
  text-align: center;
}

.badge.danger {
  background-color: #ffecec;
  color: #f56c6c;
}

.badge.warning {
  background-color: #fff8e6;
  color: #e6a23c;
}

.badge.success {
  background-color: #f0f9eb;
  color: #67c23a;
}

/* 错误模块 */
.error {
  border-color: #ffecec;
}

.error .module-icon {
  background-color: #f56c6c;
}

.error-message {
  padding: 24rpx;
  color: #f56c6c;
  font-size: 28rpx;
  line-height: 1.5;
}

/* 格式化文本模块 */
.formatted-text {
  font-size: 28rpx;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

.btn-action {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  color: #333;
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  line-height: 1.5;
  border-radius: 6rpx;
}

.btn-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  background-color: #666;
  border-radius: 50%;
}

/* 合同类型模块 */
.type-icon {
  background-color: #409eff;
}

.type-tag {
  display: inline-block;
  padding: 10rpx 20rpx;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 6rpx;
  font-size: 28rpx;
}

/* 总体评估模块 */
.assessment-icon {
  background-color: #909399;
}

.assessment-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  text-align: justify;
}

/* 合同条款模块 */
.clause-icon {
  background-color: #67c23a;
}

.clause {
  margin-bottom: 30rpx;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
}

.clause:last-child {
  margin-bottom: 0;
}

.clause-header {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #f9f9f9;
}

.clause-number {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #67c23a;
  color: #fff;
  border-radius: 50%;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.clause-title {
  font-size: 28rpx;
  font-weight: 500;
  flex: 1;
}

.risk-badge {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  color: #fff;
}

.risk-badge.high {
  background-color: #f56c6c;
}

.risk-badge.medium {
  background-color: #e6a23c;
}

.risk-badge.low {
  background-color: #67c23a;
}

.clause-body {
  padding: 16rpx 20rpx;
}

.clause-row {
  margin-bottom: 16rpx;
}

.clause-row:last-child {
  margin-bottom: 0;
}

.clause-label {
  display: block;
  font-size: 26rpx;
  color: #888;
  margin-bottom: 6rpx;
}

.clause-value {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.analysis-text {
  color: #606266;
}

/* 风险点模块 */
.risk-icon {
  background-color: #f56c6c;
}

.risk-point {
  margin-bottom: 30rpx;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1rpx solid #ffecec;
}

.risk-point:last-child {
  margin-bottom: 0;
}

.risk-header {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #ffecec;
}

.risk-number {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f56c6c;
  color: #fff;
  border-radius: 50%;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.risk-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #f56c6c;
}

.risk-body {
  padding: 16rpx 20rpx;
}

.risk-row {
  margin-bottom: 16rpx;
}

.risk-row:last-child {
  margin-bottom: 0;
}

.risk-label {
  display: block;
  font-size: 26rpx;
  color: #888;
  margin-bottom: 6rpx;
}

.risk-value {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.impact-text {
  color: #f56c6c;
}

.suggestion-text {
  color: #67c23a;
}

/* 建议修改模块 */
.suggestion-icon {
  background-color: #e6a23c;
}

.suggestion-item {
  margin-bottom: 30rpx;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1rpx solid #fdf6ec;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-header {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #fdf6ec;
}

.suggestion-number {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e6a23c;
  color: #fff;
  border-radius: 50%;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.suggestion-location {
  font-size: 28rpx;
  font-weight: 500;
  color: #e6a23c;
}

.suggestion-body {
  padding: 16rpx 20rpx;
}

.suggestion-row {
  margin-bottom: 16rpx;
  padding: 10rpx;
  border-radius: 4rpx;
}

.suggestion-row:last-child {
  margin-bottom: 0;
}

.suggestion-row.highlight {
  background-color: #fdf6ec;
}

.suggestion-label {
  display: block;
  font-size: 26rpx;
  color: #888;
  margin-bottom: 6rpx;
}

.suggestion-value {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.original-text {
  text-decoration: line-through;
  color: #f56c6c;
}

.suggested-text {
  font-weight: 500;
  color: #67c23a;
}

.reason-text {
  font-style: italic;
}

/* 底部固定按钮 */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 40rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.bottom-buttons {
  display: flex;
  justify-content: space-around;
}

.bottom-buttons .btn-primary {
  min-width: 240rpx;
  margin: 0 10rpx;
}

.btn-primary {
  background-color: #333;
  color: #fff;
  border-radius: 8rpx;
  padding: 16rpx 0;
  font-size: 28rpx;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #eee;
  margin: 20rpx 0 30rpx;
}

/* 合同关键信息样式 */
.key-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.key-info-item {
  background-color: #f9f9f9;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #409eff;
}

.key-info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.key-info-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.key-info-value.highlight {
  color: #409eff;
  font-weight: bold;
}

.key-info-section {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #409eff;
}

.key-info-section-header {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
}

.key-info-list {
  margin-top: 16rpx;
}

.key-info-list-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.key-info-list-bullet {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #409eff;
  margin-right: 12rpx;
  margin-top: 14rpx;
  flex-shrink: 0;
}

.key-info-list-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.key-info-parties {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20rpx;
}

.key-info-party {
  flex: 1;
  min-width: 300rpx;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 8rpx;
  border: 1rpx solid #eee;
}

.key-info-party-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.key-info-party-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.key-info-table {
  margin-top: 30rpx;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1rpx solid #eee;
}

.key-info-row {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.key-info-row:last-child {
  border-bottom: none;
}

.key-info-table-label {
  flex: 0 0 200rpx;
  padding: 16rpx 20rpx;
  background-color: #f9f9f9;
  font-size: 26rpx;
  color: #666;
  border-right: 1rpx solid #eee;
}

.key-info-table-value {
  flex: 1;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.key-info-table-value.important {
  color: #f56c6c;
  font-weight: 500;
}

/* 新增样式 - 复制按钮 - 黑白简约主题 */
.copy-btn {
  background: #333333;
  color: white;
  padding: 8rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
  white-space: nowrap;
  border: 2rpx solid #333333;
}

.copy-btn:active {
  background: #666666;
  border-color: #666666;
}

/* 文件信息网格 */
.file-info-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-label {
  font-weight: bold;
  color: #666;
  font-size: 28rpx;
}

.info-value {
  color: #333;
  font-size: 28rpx;
  text-align: right;
  flex: 1;
  margin-left: 40rpx;
}

/* 模块标题样式调整 */
.module-title {
  flex: 1;
}

/* 目标定位和第一印象样式 */
.target-position, .first-impression {
  line-height: 1.6;
  color: #333;
  font-size: 28rpx;
}

/* 结果页面标题样式 - 黑白简约主题 */
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background: #ffffff;
  color: #333333;
  border-bottom: 2rpx solid #f0f0f0;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

/* 完整分析内容样式 - 支持主界面滑动 */
.full-analysis {
  padding: 20rpx 0;
}

.analysis-content {
  line-height: 1.8;
  color: #333333;
  font-size: 28rpx;
  white-space: pre-wrap;
  word-break: break-word;
  /* 移除内部滚动，让内容跟随主界面滑动 */
  max-height: none;
  overflow: visible;
}

/* 错误信息样式 - 黑白简约主题 */
.error-message {
  padding: 40rpx;
  text-align: center;
  background: #f8f8f8;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  margin: 20rpx 0;
}

.error-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 原始响应样式（调试用） */
.raw-response {
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
  line-height: 1.4;
  color: #666;
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  white-space: pre-wrap;
  word-break: break-all;
}

/* Markdown样式 */
.markdown-content {
  padding: 20rpx 0;
}

/* 标题样式 */
.md-title {
  font-weight: bold;
  margin: 30rpx 0 20rpx 0;
  color: #333333;
  line-height: 1.4;
}

.md-title-1 {
  font-size: 36rpx;
  border-bottom: 3rpx solid #333333;
  padding-bottom: 10rpx;
}

.md-title-2 {
  font-size: 32rpx;
  border-bottom: 2rpx solid #666666;
  padding-bottom: 8rpx;
}

.md-title-3 {
  font-size: 30rpx;
  border-bottom: 1rpx solid #999999;
  padding-bottom: 6rpx;
}

/* 段落样式 */
.md-paragraph {
  margin: 16rpx 0;
  line-height: 1.8;
  color: #333333;
  font-size: 28rpx;
}

/* 文本样式 */
.md-text {
  color: #333333;
}

/* 加粗样式 */
.md-bold {
  font-weight: bold;
  color: #000000;
}

/* 列表样式 */
.md-list-item {
  display: flex;
  margin: 12rpx 0;
  line-height: 1.8;
  color: #333333;
  font-size: 28rpx;
}

.md-bullet {
  color: #666666;
  margin-right: 8rpx;
  flex-shrink: 0;
}

/* 换行样式 */
.md-break {
  height: 20rpx;
}

/* 备用内容样式 */
.fallback-content {
  padding: 20rpx 0;
}

.fallback-content .analysis-content {
  line-height: 1.8;
  color: #333333;
  font-size: 28rpx;
  white-space: pre-wrap;
  word-break: break-word;
}