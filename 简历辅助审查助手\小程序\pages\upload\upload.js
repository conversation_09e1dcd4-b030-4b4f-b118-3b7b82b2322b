const app = getApp();

Page({
  data: {
    files: [],
    uploading: false,
    uploadProgress: 0,
    totalFiles: 0,
    processedFiles: 0,
    stats: null, // 分析统计数据
    mergeFiles: false, // 是否将文件合并为一个项目
    showAdvancedOptions: false, // 是否展示高级选项
    analysisType: 'general', // 分析类型：general-通用分析, technical-技术岗位
    customRequirements: '', // 自定义需求
    customRequirementsLength: 0, // 自定义需求字数
    jdDescription: '', // JD岗位描述
    jdDescriptionLength: 0 // JD描述字数
  },

  onLoad: function() {
    // 加载统计数据
    this.loadStats();
  },

  onShow: function() {
    // 每次页面显示时重新加载统计数据
    this.loadStats();
  },

  // 用户点击右上角分享给好友
  onShareAppMessage: function() {
    return {
      title: 'AI简历审查助手',
      path: '/pages/upload/upload'
    };
  },

  // 用户点击右上角分享到朋友圈
  onShareTimeline: function() {
    return {
      title: 'AI简历审查助手',
      query: ''
    };
  },

  // 加载统计数据
  loadStats: function() {
    const that = this;
    wx.request({
      url: app.globalData.apiBaseUrl + '/api/stats.php',
      method: 'GET',
      success: function(res) {
        console.log('统计数据获取成功', res);
        if (res.data && res.data.success) {
          that.setData({
            stats: res.data.data
          });
        }
      },
      fail: function(error) {
        console.error('统计数据获取失败', error);
      }
    });
  },

  // 选择文件
  chooseFile: function() {
    let that = this;
    
    // 获取当前已有的文件
    let currentFiles = this.data.files || [];
    
    // 计算当前剩余可添加数量
    const remainingCount = 9 - currentFiles.length;
    
    // 如果剩余数量小于等于0，提示用户并返回
    if (remainingCount <= 0) {
      wx.showToast({
        title: '最多只能上传9个文件',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseMessageFile({
      count: remainingCount, // 可选择的文件数量为剩余数量
      type: 'file',
      extension: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
      success: function(res) {
        console.log('选择文件成功', res);
        // 将新文件追加到现有文件列表
        that.setData({
          files: currentFiles.concat(res.tempFiles)
        });
      },
      fail: function(error) {
        console.error('选择文件失败', error);
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        });
      }
    });
  },

  // 拍照上传
  takePhotos: function() {
    let that = this;
    wx.showActionSheet({
      itemList: ['拍摄照片', '从相册选择'],
      success: function(res) {
        if (res.tapIndex === 0) {
          // 使用相机拍照
          that.useCamera();
        } else if (res.tapIndex === 1) {
          // 从相册选择
          that.chooseFromAlbum();
        }
      }
    });
  },

  // 使用相机拍照
  useCamera: function() {
    let that = this;
    wx.chooseMedia({
      count: 9, // 最多可选择9张图片
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: function(res) {
        console.log('拍照成功', res);
        // 处理返回的图片
        that.processChosenPhotos(res.tempFiles);
      },
      fail: function(error) {
        console.error('拍照失败', error);
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      }
    });
  },

  // 从相册选择照片
  chooseFromAlbum: function() {
    let that = this;
    wx.chooseMedia({
      count: 9, // 最多可选择9张图片
      mediaType: ['image'],
      sourceType: ['album'],
      success: function(res) {
        console.log('选择相册图片成功', res);
        // 处理返回的图片
        that.processChosenPhotos(res.tempFiles);
      },
      fail: function(error) {
        console.error('选择相册图片失败', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理选择的照片
  processChosenPhotos: function(tempFiles) {
    // 获取当前已有的文件
    let currentFiles = this.data.files || [];
    
    // 计算当前剩余可添加数量
    const remainingCount = 9 - currentFiles.length;
    
    // 如果剩余数量小于等于0，提示用户并返回
    if (remainingCount <= 0) {
      wx.showToast({
        title: '最多只能上传9个文件',
        icon: 'none'
      });
      return;
    }
    
    // 如果选择的文件超过剩余数量，只取剩余数量的文件
    const filesToAdd = tempFiles.length > remainingCount ? tempFiles.slice(0, remainingCount) : tempFiles;
    
    // 如果选择的文件被截断，通知用户
    if (tempFiles.length > remainingCount) {
      wx.showToast({
        title: `已达上限，只添加了${remainingCount}张`,
        icon: 'none'
      });
    }
    
    // 转换为与文件选择器相同的格式
    let formattedFiles = filesToAdd.map((item, index) => {
      const fileName = `photo_${Date.now()}_${index}.${item.fileType === 'image/png' ? 'png' : 'jpg'}`;
      return {
        path: item.tempFilePath,
        size: item.size,
        name: fileName,
        time: Date.now()
      };
    });
    
    // 将新文件追加到现有文件列表
    this.setData({
      files: currentFiles.concat(formattedFiles)
    });
  },

  // 切换合并开关
  toggleMerge: function() {
    this.setData({
      mergeFiles: !this.data.mergeFiles
    });
    wx.showToast({
      title: this.data.mergeFiles ? '已开启合并模式' : '已关闭合并模式',
      icon: 'none'
    });
  },
  
  // 切换高级选项显示状态
  toggleAdvancedOptions: function() {
    this.setData({
      showAdvancedOptions: !this.data.showAdvancedOptions
    });
  },
  
  // 选择分析类型
  selectAnalysisType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      analysisType: type
    });
  },
  
  // 更新自定义需求
  updateCustomRequirements: function(e) {
    const value = e.detail.value;
    this.setData({
      customRequirements: value,
      customRequirementsLength: value.length
    });
  },

  // 更新JD描述
  updateJdDescription: function(e) {
    const value = e.detail.value;
    this.setData({
      jdDescription: value,
      jdDescriptionLength: value.length
    });
  },

  // 上传文件
  uploadFiles: function() {
    if (this.data.files.length === 0) {
      wx.showToast({
        title: '请先选择文件',
        icon: 'none'
      });
      return;
    }

    // 显示加载中动画
    wx.showLoading({
      title: '准备上传...',
      mask: true  // 使用mask可以防止用户触摸屏幕
    });

    this.setData({
      uploading: true,
      uploadProgress: 0,
      totalFiles: this.data.files.length,
      processedFiles: 0
    });

    app.globalData.uploadedFiles = this.data.files;
    app.globalData.analysisResults = [];

    // 根据是否合并决定上传方式
    if (this.data.mergeFiles && this.data.files.length > 1) {
      // 合并上传模式
      this.uploadMergedFiles();
    } else {
      // 分开上传模式（原模式）
      this.uploadNextFile(0);
    }
  },

  // 合并上传文件
  uploadMergedFiles: function() {
    const that = this;
    let filesUploaded = 0;
    const totalFiles = this.data.files.length;
    
    // 显示加载动画
    wx.showLoading({
      title: '准备合并上传...',
      mask: true
    });

    // 准备数据，创建一个包含合并标志的数据
    const mergedProjectName = `合并项目_${Date.now()}`;
    
    // 保存第一个文件名作为项目名
    let projectFileName = this.data.files[0].name;
    
    // 创建上传任务数组
    const uploadTasks = this.data.files.map((file, index) => {
      return new Promise((resolve, reject) => {
        wx.uploadFile({
          url: app.globalData.apiBaseUrl + '/api/upload.php',
          filePath: file.path,
          name: 'file',
          formData: {
            // 添加合并标记和项目ID
            merged: 'true',
            projectId: mergedProjectName,
            fileIndex: index.toString(),
            totalFiles: totalFiles.toString(),
            analysisType: that.data.analysisType,
            customRequirements: that.data.customRequirements,
            jd: that.data.jdDescription,
            // 添加本地API回调地址
            callback_url: app.globalData.apiBaseUrl + '/api/save_analysis.php'
          },
          success: function(res) {
            try {
              console.log(`文件 ${index+1}/${totalFiles} 上传成功`, res);
              const result = JSON.parse(res.data);
              resolve(result);
            } catch (e) {
              console.error('解析结果失败', e);
              reject(e);
            }
          },
          fail: function(error) {
            console.error(`文件 ${index+1}/${totalFiles} 上传失败`, error);
            reject(error);
          }
        });
      });
    });

    // 更新进度显示函数
    const updateProgress = (completed) => {
      that.setData({
        processedFiles: completed,
        uploadProgress: (completed / totalFiles) * 100
      });
      
      // 更新加载提示
      wx.showLoading({
        title: `处理文件 ${completed}/${totalFiles}`,
        mask: true
      });
    };

    // 执行所有上传任务
    Promise.all(uploadTasks.map((task, i) => {
      return task.then(result => {
        filesUploaded++;
        updateProgress(filesUploaded);
        return result;
      }).catch(err => {
        filesUploaded++;
        updateProgress(filesUploaded);
        return {error: `文件上传失败: ${err.errMsg || err.message || '未知错误'}`};
      });
    })).then(results => {
      // 所有文件上传完成
      wx.hideLoading();
      
      // 添加一个合并后的结果到全局数据
      app.globalData.analysisResults = [{
        fileName: `${projectFileName} (合并项目，共${totalFiles}个文件)`,
        result: results[0], // 使用第一个文件的分析结果
        mergedProject: true,
        mergedResults: results
      }];
      
      // 跳转到结果页面
      that.setData({
        uploading: false
      });
      
      // 重新加载统计数据
      that.loadStats();
      
      wx.navigateTo({
        url: '/pages/result/result'
      });
    }).catch(error => {
      wx.hideLoading();
      console.error('合并上传过程中出错', error);
      wx.showToast({
        title: '上传过程中出错',
        icon: 'none'
      });
      
      that.setData({
        uploading: false
      });
    });
  },

  // 递归上传文件
  uploadNextFile: function(index) {
    if (index >= this.data.files.length) {
      // 所有文件上传完成，跳转到结果页面
      this.setData({
        uploading: false
      });
      
      // 重新加载统计数据
      this.loadStats();
      
      // 隐藏加载动画
      wx.hideLoading();
      
      wx.navigateTo({
        url: '/pages/result/result'
      });
      return;
    }

    const file = this.data.files[index];
    const that = this;

    // 更新加载提示
    wx.showLoading({
      title: `处理文件 ${index + 1}/${this.data.files.length}`,
      mask: true
    });

    // 上传单个文件
    wx.uploadFile({
      url: app.globalData.apiBaseUrl + '/api/upload.php',
      filePath: file.path,
      name: 'file',
      formData: {
        analysisType: that.data.analysisType,
        customRequirements: that.data.customRequirements,
        jd: that.data.jdDescription,
        // 添加本地API回调地址
        callback_url: app.globalData.apiBaseUrl + '/api/save_analysis.php'
      },
      success: function(res) {
        console.log('文件上传成功', res);
        try {
          const result = JSON.parse(res.data);
          app.globalData.analysisResults.push({
            fileName: file.name,
            result: result
          });
        } catch (e) {
          console.error('解析结果失败', e);
          app.globalData.analysisResults.push({
            fileName: file.name,
            result: {
              error: '解析结果失败'
            }
          });
        }
      },
      fail: function(error) {
        console.error('文件上传失败', error);
        app.globalData.analysisResults.push({
          fileName: file.name,
          result: {
            error: '文件上传失败: ' + error.errMsg
          }
        });
        
        // 显示错误提示
        wx.showToast({
          title: '文件上传失败',
          icon: 'none',
          duration: 2000
        });
      },
      complete: function() {
        // 更新进度
        that.setData({
          processedFiles: that.data.processedFiles + 1,
          uploadProgress: ((that.data.processedFiles + 1) / that.data.totalFiles) * 100
        });

        // 上传下一个文件
        that.uploadNextFile(index + 1);
      }
    });
  },

  // 清除所有文件
  clearFiles: function() {
    wx.showModal({
      title: '清除文件',
      content: '确定要清除所有已选择的文件吗？',
      success: res => {
        if (res.confirm) {
          this.setData({
            files: []
          });
          wx.showToast({
            title: '已清除所有文件',
            icon: 'success'
          });
        }
      }
    });
  }
}); 