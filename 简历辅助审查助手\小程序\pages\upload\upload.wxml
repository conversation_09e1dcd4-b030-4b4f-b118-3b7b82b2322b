<!--pages/upload/upload.wxml-->
<wxs src="../../utils/format.wxs" module="format" />

<view class="container">
  <view class="card">
    <!--<view class="title text-center">简历审查助手</view>-->
    <view class="subtitle text-center">上传简历文件进行AI分析</view>
    
    <!-- 统计数据展示 -->
    <view class="stats-card" wx:if="{{stats}}">
      <view class="stats-title">平台分析统计</view>
      <view class="stats-row">
        <view class="stats-item">
          <view class="stats-number">{{stats.contractCount || 0}}</view>
          <view class="stats-label">已分析简历</view>
        </view>
        <view class="stats-item highlight">
          <view class="stats-number">{{stats.riskCount || 0}}</view>
          <view class="stats-label">发现问题点</view>
        </view>
      </view>
    </view>
    
    <view class="file-list" wx:if="{{files.length > 0}}">
      <view class="file-header">
        <view class="subtitle">已选择 {{files.length}} 个文件:</view>
        <view class="clear-btn" bindtap="clearFiles">清空</view>
      </view>
      <view class="file-item" wx:for="{{files}}" wx:key="path">
        <view class="file-name">{{item.name}}</view>
        <view class="file-size">{{format.formatFileSize(item.size)}} KB</view>
      </view>
      
      <!-- 合并上传开关 -->
      <view class="merge-switch" wx:if="{{files.length > 1}}">
        <view class="merge-label">将多个文件合并为一个项目</view>
        <switch checked="{{mergeFiles}}" bindchange="toggleMerge" color="#333"/>
      </view>
    </view>
    
    <!-- JD岗位描述输入 -->
    <view class="jd-input-section">
      <view class="jd-label">岗位描述 (JD) - 可选</view>
      <textarea class="jd-textarea" placeholder="请粘贴您要应聘的岗位描述(JD)，这将帮助AI更精准地分析简历匹配度" value="{{jdDescription}}" bindinput="updateJdDescription" maxlength="1200"></textarea>
      <view class="char-count">{{jdDescriptionLength}}/1200</view>
    </view>

    <!-- 高级选项 -->
    <view class="advanced-options">
      <view class="options-header" bindtap="toggleAdvancedOptions">
        <view class="options-title">高级分析选项</view>
        <view class="options-toggle {{showAdvancedOptions ? 'expanded' : ''}}">▼</view>
      </view>

      <view class="options-content" wx:if="{{showAdvancedOptions}}">
        <view class="option-section">
          <view class="option-label">分析重点:</view>
          <view class="role-options">
            <view class="role-option {{analysisType === 'general' ? 'active' : ''}}" data-type="general" bindtap="selectAnalysisType">
              <view class="role-radio {{analysisType === 'general' ? 'checked' : ''}}"></view>
              <text>通用分析</text>
            </view>
            <view class="role-option {{analysisType === 'technical' ? 'active' : ''}}" data-type="technical" bindtap="selectAnalysisType">
              <view class="role-radio {{analysisType === 'technical' ? 'checked' : ''}}"></view>
              <text>技术岗位</text>
            </view>
          </view>
        </view>

        <view class="option-section">
          <view class="option-label">特殊要求:</view>
          <textarea class="custom-requirements" placeholder="请输入您的特定分析需求，如重点关注某个技能、经验等" value="{{customRequirements}}" bindinput="updateCustomRequirements" maxlength="500"></textarea>
          <view class="char-count">{{customRequirementsLength}}/500</view>
        </view>
      </view>
    </view>
    
    <view class="btn-group">
      <button class="btn btn-choose" bindtap="chooseFile">
        <view class="btn-icon file-icon"></view>
        <text>选择文件</text>
      </button>
      <button class="btn btn-camera" bindtap="takePhotos">
        <view class="btn-icon camera-icon"></view>
        <text>拍照上传</text>
      </button>
      <button class="btn primary" bindtap="uploadFiles" disabled="{{uploading || files.length === 0}}">
        <view class="btn-icon upload-icon"></view>
        <text>开始分析</text>
      </button>
    </view>
    
    <view class="upload-progress" wx:if="{{uploading}}">
      <view class="loading-container">
        <view class="loading-icon"></view>
        <text class="loading-text">正在处理文件，请稍候...</text>
      </view>
      <progress percent="{{uploadProgress}}" stroke-width="4" activeColor="#333" backgroundColor="#f0f0f0" active show-info />
      <view class="progress-text">处理进度: {{processedFiles}}/{{totalFiles}}</view>
    </view>
  </view>
  
  <view class="card tips">
    <view class="tips-title">上传注意事项</view>
    <view class="tips-content">
      <view class="tip-item">
        <view class="tip-icon doc-icon"></view>
        <text>简历文档: PDF, DOC, DOCX</text>
      </view>
      <view class="tip-item">
        <view class="tip-icon img-icon"></view>
        <text>简历图片: JPG, PNG</text>
      </view>
      <view class="tip-item">
        <view class="tip-icon size-icon"></view>
        <text>单个文件最大100MB</text>
      </view>
      <view class="tip-item">
        <view class="tip-icon limit-icon"></view>
        <text>建议上传1个简历文件</text>
      </view>
    </view>
  </view>
</view> 