/* pages/upload/upload.wxss */
.container {
  padding: 30rpx 20rpx;
}

.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.subtitle {
  font-size: 32rpx;
  margin-bottom: 30rpx;
  color: #333;
}

.text-center {
  text-align: center;
}

/* 统计数据卡片 */
.stats-card {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 12rpx;
  margin: 30rpx 0;
}

.stats-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  text-align: center;
}

.stats-row {
  display: flex;
  justify-content: space-around;
}

.stats-item {
  text-align: center;
  padding: 10rpx 20rpx;
}

.stats-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.stats-item.highlight .stats-number {
  color: #f56c6c;
}

/* 按钮样式 */
.btn-group {
  display: flex;
  justify-content: space-between;
  margin: 40rpx 0;
}

.btn {
  flex: 1;
  margin: 0 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  background-color: #f5f5f5;
  color: #333;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.btn-choose {
  margin-right: 10rpx;
}

.btn-camera {
  margin-left: 10rpx;
  margin-right: 10rpx;
}

.btn:first-child {
  margin-left: 0;
}

.btn:last-child {
  margin-right: 0;
}

.btn.primary {
  background-color: #333;
  color: #fff;
}

.btn[disabled] {
  background-color: #f5f5f5 !important;
  color: #ccc !important;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0.7;
}

/* 自定义图标 */
.camera-icon {
  background-color: #7c4dff;
  position: relative;
}

/* 文件列表 */
.file-list {
  margin: 30rpx 0;
  max-height: 400rpx;
  overflow-y: auto;
}

/* 文件列表头部 */
.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.file-header .subtitle {
  margin-bottom: 0;
}

.clear-btn {
  font-size: 26rpx;
  color: #f56c6c;
  padding: 8rpx 16rpx;
  border: 1rpx solid #f56c6c;
  border-radius: 30rpx;
}

.file-item {
  padding: 20rpx;
  background-color: #f9f9f9;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-left: 4rpx solid #333;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 24rpx;
  color: #666;
}

/* 上传进度 */
.upload-progress {
  margin: 30rpx 0;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  border-left: 4rpx solid #333;
}

.loading-container {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}

.progress-text {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-top: 15rpx;
  font-weight: 500;
}

/* 提示卡片 */
.tips {
  background-color: #f9f9f9;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  display: flex;
  align-items: center;
}

.tip-icon {
  width: 10rpx;
  height: 10rpx;
  margin-right: 10rpx;
  background-color: #333;
  border-radius: 50%;
}

/* 合并开关样式 */
.merge-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  border-left: 4rpx solid #7c4dff;
}

.merge-label {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 高级选项样式 */
.advanced-options {
  margin: 30rpx 0;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  overflow: hidden;
  border-left: 4rpx solid #7c4dff;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  cursor: pointer;
}

.options-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.options-toggle {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.options-toggle.expanded {
  transform: rotate(180deg);
}

.options-content {
  padding: 0 20rpx 20rpx;
}

.option-section {
  margin-bottom: 20rpx;
}

.option-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.role-options {
  display: flex;
  gap: 30rpx;
}

.role-option {
  display: flex;
  align-items: center;
  padding: 10rpx 0;
  font-size: 26rpx;
  color: #333;
}

.role-radio {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  margin-right: 10rpx;
  position: relative;
  transition: all 0.2s;
}

.role-radio.checked {
  border-color: #7c4dff;
  background-color: #fff;
}

.role-radio.checked::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #7c4dff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.role-option.active {
  color: #7c4dff;
}

.custom-requirements {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #fff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
} 