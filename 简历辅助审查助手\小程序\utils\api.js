/**
 * 合同审查API工具类
 */

const app = getApp();

/**
 * 上传合同文件进行审查
 * @param {Object} options 选项对象
 * @param {String} options.filePath 文件路径
 * @param {String} options.fileType 文件类型
 * @param {String} options.prompt 自定义提示词
 * @param {Function} options.onProgress 上传进度回调
 * @param {Function} options.onSuccess 上传成功回调
 * @param {Function} options.onFail 上传失败回调
 */
const uploadContractFile = (options) => {
  const { filePath, fileType, prompt, onProgress, onSuccess, onFail } = options;
  
  // 文件名提取
  const fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
  
  // 上传任务
  const uploadTask = wx.uploadFile({
    url: `${app.globalData.apiBaseUrl}/hetong.php`,
    filePath: filePath,
    name: 'file', // 服务器接收的文件参数名
    formData: {
      prompt: prompt || '' // 自定义提示词，如果为空则使用服务器默认提示词
    },
    success: (res) => {
      if (res.statusCode === 200) {
        try {
          const result = JSON.parse(res.data);
          onSuccess && onSuccess(result);
        } catch (e) {
          onFail && onFail({
            message: '返回数据格式错误',
            error: e,
            rawData: res.data
          });
        }
      } else {
        onFail && onFail({
          message: `请求失败，状态码：${res.statusCode}`,
          statusCode: res.statusCode,
          data: res.data
        });
      }
    },
    fail: (error) => {
      onFail && onFail({
        message: '上传失败',
        error: error
      });
    }
  });
  
  // 监听上传进度
  if (onProgress) {
    uploadTask.onProgressUpdate((res) => {
      onProgress(res.progress);
    });
  }
  
  // 返回上传任务，便于调用方取消上传
  return uploadTask;
};

/**
 * 下载PDF报告
 * @param {Object} options 选项对象
 * @param {String} options.url PDF文件URL
 * @param {String} options.fileName 保存的文件名
 * @param {Function} options.onProgress 下载进度回调
 * @param {Function} options.onSuccess 下载成功回调
 * @param {Function} options.onFail 下载失败回调
 */
const downloadPdfReport = (options) => {
  const { url, fileName, onProgress, onSuccess, onFail } = options;
  
  // 文件保存路径
  const filePath = `${wx.env.USER_DATA_PATH}/${fileName || 'contract_analysis.pdf'}`;
  
  // 下载任务
  const downloadTask = wx.downloadFile({
    url: url,
    filePath: filePath,
    success: (res) => {
      if (res.statusCode === 200) {
        onSuccess && onSuccess({
          tempFilePath: res.tempFilePath,
          savedFilePath: filePath
        });
      } else {
        onFail && onFail({
          message: `下载失败，状态码：${res.statusCode}`,
          statusCode: res.statusCode
        });
      }
    },
    fail: (error) => {
      onFail && onFail({
        message: '下载失败',
        error: error
      });
    }
  });
  
  // 监听下载进度
  if (onProgress) {
    downloadTask.onProgressUpdate((res) => {
      onProgress(res.progress);
    });
  }
  
  // 返回下载任务，便于调用方取消下载
  return downloadTask;
};

module.exports = {
  uploadContractFile,
  downloadPdfReport
}; 