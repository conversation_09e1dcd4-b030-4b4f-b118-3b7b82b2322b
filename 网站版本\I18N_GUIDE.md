# 多语言支持完整指南

## 📋 当前状态

✅ **已完成的翻译**：
- 主页 (index.html) - 100% 完成
- 结果页 (result.html) - 100% 完成
- 8种语言支持：英语、中文简体、中文繁体、日语、韩语、德语、俄语、法语
- API多语言响应支持

## 🛠️ 使用自动化更新脚本

### 方法1：浏览器控制台（推荐）

1. **打开页面**：在浏览器中打开需要检查的页面
2. **打开控制台**：按 F12 打开开发者工具，切换到 Console 标签
3. **加载脚本**：复制 `update_html_i18n.js` 的内容并粘贴到控制台
4. **运行更新**：
   ```javascript
   // 自动更新所有缺失的翻译
   updateHTMLForI18n();
   
   // 或使用对象方法
   i18nUpdater.update();
   ```
5. **验证结果**：
   ```javascript
   // 检查是否有遗漏的中文内容
   i18nUpdater.validate();
   ```
6. **立即应用**：
   ```javascript
   // 立即应用翻译（如果页面已加载多语言管理器）
   window.langManager?.updatePageContent();
   ```

### 方法2：直接在页面中使用

1. **添加脚本引用**：
   ```html
   <script src="update_html_i18n.js"></script>
   ```
2. **在页面加载后调用**：
   ```javascript
   document.addEventListener('DOMContentLoaded', function() {
       // 先更新翻译属性
       updateHTMLForI18n();
       
       // 再初始化多语言管理器
       if (window.langManager) {
           window.langManager.updatePageContent();
       }
   });
   ```

## 🔍 手动检查和修复

### 查找未翻译的中文内容

使用浏览器开发者工具的搜索功能：

1. **打开Elements面板**
2. **使用搜索**：Ctrl+F 搜索中文字符
3. **正则表达式搜索**：启用正则表达式，搜索 `[\u4e00-\u9fff]+`

### 手动添加翻译属性

对于发现的中文内容，按以下步骤处理：

1. **确定翻译键**：在 `js/i18n.js` 中查找或添加对应的翻译键
2. **添加属性**：给元素添加 `data-i18n="translation_key"`
3. **更新内容**：将中文内容改为英文默认内容

示例：
```html
<!-- 修改前 -->
<h2>关于我们</h2>

<!-- 修改后 -->
<h2 data-i18n="about_title">About Us</h2>
```

## 📝 添加新翻译

### 1. 在语言包中添加翻译

编辑 `js/i18n.js`，在所有语言对象中添加新的翻译键：

```javascript
// 英语
'en': {
    'new_key': 'English Text',
    // ... 其他翻译
},

// 中文简体
'zh-CN': {
    'new_key': '中文文本',
    // ... 其他翻译
},

// 其他语言...
```

### 2. 在HTML中使用

```html
<span data-i18n="new_key">English Text</span>
```

### 3. 在JavaScript中使用

```javascript
const text = window.langManager.t('new_key');
```

## 🌍 语言切换

### 自动检测

系统会按以下优先级检测语言：
1. URL参数：`?lang=zh-CN`
2. localStorage保存的偏好
3. 浏览器语言设置

### 手动切换

```javascript
// 切换到中文简体
window.langManager.setLanguage('zh-CN');

// 切换到日语
window.langManager.setLanguage('ja');

// 获取当前语言
const currentLang = window.langManager.getCurrentLanguage();
```

### 支持的语言代码

| 语言 | 代码 | 示例 |
|------|------|------|
| 英语 | `en` | English |
| 中文简体 | `zh-CN` | 中文简体 |
| 中文繁体 | `zh-TW` | 中文繁體 |
| 日语 | `ja` | 日本語 |
| 韩语 | `ko` | 한국어 |
| 德语 | `de` | Deutsch |
| 俄语 | `ru` | Русский |
| 法语 | `fr` | Français |

## 🔧 故障排除

### 常见问题

1. **翻译不生效**
   - 检查 `js/i18n.js` 是否正确加载
   - 确认元素有正确的 `data-i18n` 属性
   - 检查翻译键是否存在于语言包中

2. **部分内容未翻译**
   - 运行 `i18nUpdater.validate()` 检查遗漏
   - 手动添加缺失的翻译属性

3. **新语言不显示**
   - 确认语言代码正确
   - 检查语言包是否完整
   - 验证语言检测逻辑

### 调试方法

```javascript
// 检查当前语言
console.log('当前语言:', window.langManager.getCurrentLanguage());

// 检查翻译
console.log('翻译测试:', window.langManager.t('hero_title'));

// 检查所有多语言元素
console.log('多语言元素:', document.querySelectorAll('[data-i18n]').length);

// 强制更新页面翻译
window.langManager.updatePageContent();
```

## 📊 翻译完整性检查

### 自动检查脚本

```javascript
function checkTranslationCompleteness() {
    const languages = ['en', 'zh-CN', 'zh-TW', 'ja', 'ko', 'de', 'ru', 'fr'];
    const allKeys = new Set();
    
    // 收集所有翻译键
    languages.forEach(lang => {
        if (i18n[lang]) {
            Object.keys(i18n[lang]).forEach(key => allKeys.add(key));
        }
    });
    
    // 检查每种语言的完整性
    languages.forEach(lang => {
        const missing = [];
        allKeys.forEach(key => {
            if (!i18n[lang] || !i18n[lang][key]) {
                missing.push(key);
            }
        });
        
        if (missing.length > 0) {
            console.warn(`${lang} 缺失翻译:`, missing);
        } else {
            console.log(`✅ ${lang} 翻译完整`);
        }
    });
}

// 运行检查
checkTranslationCompleteness();
```

## 🚀 最佳实践

1. **统一命名规范**：使用描述性的翻译键名
2. **分组管理**：按页面或功能模块组织翻译键
3. **定期检查**：使用自动化脚本定期检查翻译完整性
4. **测试多语言**：在不同语言下测试页面功能
5. **保持同步**：添加新内容时同时添加所有语言的翻译

## 📞 技术支持

如遇问题，请联系：<EMAIL>
