# AI简历优化助手

一个基于AI技术的专业简历分析与优化网站，支持多种文件格式，提供详细的简历分析和改进建议。

## 功能特色

- 🤖 **AI智能分析** - 基于Gemini AI的深度简历分析
- 📄 **多格式支持** - 支持PDF、DOC、DOCX、JPG、PNG、TXT格式
- ⚡ **快速生成** - 快速获得专业的分析结果
- 💡 **专业建议** - 提供具体的优化建议和修改方案
- 📊 **岗位匹配** - 结合目标岗位JD进行精准匹配分析
- 🎯 **Markdown展示** - 使用Markdown格式美化结果展示

## 技术架构

### 前端
- **HTML5** - 现代化的网页结构
- **CSS3** - 响应式设计，参考resume-now.com风格
- **JavaScript** - 原生JS实现交互功能
- **Marked.js** - Markdown解析和渲染

### 后端
- **PHP** - 服务端逻辑处理
- **MySQL** - 数据存储
- **Gemini AI API** - AI分析引擎

## 安装部署

### 1. 环境要求
- PHP 7.4+
- MySQL 5.7+
- Web服务器 (Apache/Nginx)

### 2. 数据库配置
```bash
# 导入数据库结构
mysql -u root -p < init_database.sql
```

### 3. 配置API密钥
编辑 `api.php` 文件，替换Gemini API密钥：
```php
$apiKey = 'YOUR_GEMINI_API_KEY'; // 替换为您的实际API密钥
```

### 4. 配置数据库连接
编辑 `api.php` 文件中的数据库配置：
```php
$db_config = [
    'host' => 'localhost',
    'username' => 'aakkeeco_shaw',
    'password' => 'your_password', // 请填入您的数据库密码
    'database' => 'aakkeeco_resume_analysis',
    'charset' => 'utf8mb4'
];
```

### 5. 部署文件
将所有文件上传到Web服务器目录，确保以下文件结构：
```
网站版本/
├── index.html          # 主页面
├── api.php            # API接口
├── css/
│   └── style.css      # 样式文件
├── js/
│   ├── main.js        # 主要功能
│   └── marked.min.js  # Markdown解析
├── init_database.sql  # 数据库初始化
└── README.md          # 说明文档
```

## 使用说明

### 1. 访问网站
在浏览器中打开 `index.html` 或配置的域名

### 2. 上传简历
- 点击上传区域选择文件
- 或直接拖拽文件到上传区域
- 支持的格式：PDF、DOC、DOCX、JPG、PNG、TXT
- 文件大小限制：100MB

### 3. 添加岗位描述（可选）
在文本框中粘贴目标岗位的JD描述，AI将进行更精准的匹配分析

### 4. 开始分析
点击"开始AI分析"按钮，等待AI处理（通常需要30秒到2分钟）

### 5. 查看结果
分析完成后，结果将以Markdown格式展示，包括：
- 第一印象与初步诊断
- 地毯式深度审计
- 战略性修改蓝图
- 修改后的简历范本
- 最终裁决与行动清单

## API接口

### POST /api.php
上传简历文件进行分析

**请求参数：**
- `file` (文件) - 简历文件
- `jd` (字符串，可选) - 岗位描述

**响应格式：**
```json
{
    "success": true,
    "raw_response": "AI原始响应",
    "formatted_text": "格式化文本",
    "analysis": {
        "firstImpression": "第一印象",
        "targetPosition": "目标岗位",
        "revisedResume": "修改后简历",
        "actionItems": ["行动项目"]
    },
    "file_info": {
        "name": "文件名",
        "type": "文件类型",
        "has_jd": true
    },
    "processing_time": 1500,
    "saved_id": 123
}
```

## 数据库表结构

### resume_analysis_results
| 字段 | 类型 | 说明 |
|------|------|------|
| id | int(11) | 主键ID |
| file_name | varchar(255) | 文件名 |
| file_type | varchar(100) | 文件类型 |
| has_jd | tinyint(1) | 是否包含JD |
| raw_response | longtext | AI原始响应 |
| formatted_text | longtext | 格式化文本 |
| analysis_data | longtext | 结构化数据(JSON) |
| created_at | timestamp | 创建时间 |
| ip_address | varchar(45) | IP地址 |

## 注意事项

1. **API密钥安全** - 请妥善保管Gemini API密钥，不要泄露
2. **文件安全** - 上传的文件会临时存储，建议定期清理
3. **数据隐私** - 简历数据会存储在数据库中，请注意隐私保护
4. **性能优化** - 大文件可能需要较长处理时间，建议优化文件大小

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看服务器日志

2. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务运行正常
   - 验证用户权限

3. **文件上传失败**
   - 检查文件格式是否支持
   - 确认文件大小未超限
   - 查看PHP上传配置

## 更新日志

### v1.0.0 (2025-07-29)
- 初始版本发布
- 支持多种文件格式上传
- 集成Gemini AI分析
- Markdown结果展示
- 数据库存储功能

## 许可证

本项目仅供学习和研究使用。

## 联系方式

网站地址：https://www.furrywoo.com
