# AI简历优化助手 - 功能更新说明

## 🌍 1. 多语言支持

### 新增功能
- **自动语言检测**: 根据用户浏览器语言自动切换界面语言
- **支持8种语言**: 英语、中文简体、中文繁体、日语、韩语、德语、俄语、法语
- **智能AI响应**: Gemini AI会根据用户语言返回对应语言的分析结果

### 技术实现
- 新增 `js/i18n.js` 多语言管理器
- 支持URL参数切换语言: `?lang=zh-CN`
- localStorage保存用户语言偏好
- API自动传递用户语言给Gemini

### 使用方法
```javascript
// 手动切换语言
window.langManager.setLanguage('ja'); // 切换到日语

// 获取当前语言
const currentLang = window.langManager.getCurrentLanguage();

// 获取翻译文本
const text = window.langManager.t('hero_title');
```

## 📧 2. 联系信息更新

### 新增联系方式
- **邮箱**: <EMAIL>
- 已更新到页面footer和多语言包中

## 📊 3. Token使用统计

### 新增功能
- **实时Token统计**: 记录每次API调用的输入/输出Token数量
- **历史数据追踪**: 保存所有Token使用历史
- **管理员面板**: 查看详细的Token使用统计

### 数据库更新
需要运行 `database_updates.sql` 来更新数据库结构：

```sql
-- 添加Token字段到现有表
ALTER TABLE resume_analysis_results 
ADD COLUMN input_tokens INT DEFAULT 0,
ADD COLUMN output_tokens INT DEFAULT 0;

-- 创建Token统计表
CREATE TABLE token_stats (
    id INT PRIMARY KEY DEFAULT 1,
    total_input_tokens BIGINT DEFAULT 0,
    total_output_tokens BIGINT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 管理员面板
访问 `admin/token_stats.php` 查看Token使用统计
- 默认密码: `admin123` (请修改为安全密码)
- 显示总体统计和每日使用情况

## 🎨 4. 界面优化

### 设计更新
- **黑白简约主题**: 白色为主，黑色为辅的现代化设计
- **独立结果页面**: 分析结果在专门页面显示，不再在首页展示
- **修复代码块显示**: 简历范本以正常文本显示，支持Markdown格式

### 用户体验改进
- 分析完成后自动跳转到结果页面
- 支持打印分析报告
- 响应式设计，支持移动设备

## 📁 文件结构

```
网站版本/
├── index.html              # 主页面 (已更新多语言)
├── result.html             # 结果页面 (新增)
├── api.php                 # API接口 (已更新Token统计和多语言)
├── js/
│   ├── i18n.js            # 多语言管理器 (新增)
│   ├── main.js            # 主要功能 (已更新)
│   └── marked.min.js      # Markdown解析器
├── css/
│   └── style.css          # 样式文件 (已更新黑白主题)
├── admin/
│   └── token_stats.php    # Token统计管理页面 (新增)
├── database_updates.sql    # 数据库更新脚本 (新增)
└── UPDATES_README.md      # 本说明文件
```

## 🚀 部署步骤

1. **更新数据库**:
   ```bash
   mysql -u root -p resume_analysis < database_updates.sql
   ```

2. **更新API密钥** (如需要):
   编辑 `api.php` 中的 `$apiKey` 变量

3. **修改管理员密码**:
   编辑 `admin/token_stats.php` 中的 `$admin_password` 变量

4. **测试功能**:
   - 访问主页测试多语言切换
   - 上传简历测试分析功能
   - 访问 `admin/token_stats.php` 测试统计功能

## 🔧 配置选项

### 语言配置
在 `js/i18n.js` 中可以:
- 添加新语言支持
- 修改现有翻译
- 调整语言检测逻辑

### Token统计配置
在 `api.php` 中可以:
- 修改Token统计逻辑
- 添加更多统计维度
- 配置数据保留策略

## 📈 监控和维护

### 日志文件
- `resume_api.log`: API调用日志
- 包含Token使用情况和错误信息

### 数据库监控
- 定期检查 `token_stats` 表的数据
- 监控 `resume_analysis_results` 表的增长

### 性能优化建议
- 定期清理旧的分析记录
- 监控Token使用量，避免超出API限制
- 考虑添加缓存机制

## 🐛 故障排除

### 常见问题
1. **多语言不生效**: 检查 `js/i18n.js` 是否正确加载
2. **Token统计不准确**: 检查数据库表结构是否正确更新
3. **API调用失败**: 检查Gemini API密钥是否有效

### 调试方法
- 查看浏览器控制台错误
- 检查 `resume_api.log` 日志文件
- 使用 `admin/token_stats.php` 查看统计数据

## 📞 技术支持

如有问题，请联系: <EMAIL>
