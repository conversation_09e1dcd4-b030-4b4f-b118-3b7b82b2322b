<?php
// Token统计查看页面
require_once '../api.php';

// 简单的身份验证（生产环境中应使用更安全的方法）
$admin_password = 'admin123'; // 请修改为安全的密码
$is_authenticated = false;

if (isset($_POST['password'])) {
    if ($_POST['password'] === $admin_password) {
        $is_authenticated = true;
        setcookie('admin_auth', hash('sha256', $admin_password), time() + 3600);
    }
} elseif (isset($_COOKIE['admin_auth']) && $_COOKIE['admin_auth'] === hash('sha256', $admin_password)) {
    $is_authenticated = true;
}

if (!$is_authenticated) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Token统计 - 管理员登录</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; }
            .login-form { background: #f5f5f5; padding: 30px; border-radius: 8px; }
            input[type="password"] { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
            button { width: 100%; padding: 10px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        </style>
    </head>
    <body>
        <div class="login-form">
            <h2>管理员登录</h2>
            <form method="post">
                <input type="password" name="password" placeholder="请输入管理员密码" required>
                <button type="submit">登录</button>
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 获取数据库连接
$db_config = [
    'host' => 'localhost',
    'dbname' => 'resume_analysis',
    'username' => 'root',
    'password' => '4711329.Jiang'
];

try {
    $pdo = getDbConnection($db_config);
    
    // 获取总体统计
    $totalStats = $pdo->query("SELECT * FROM token_stats WHERE id = 1")->fetch(PDO::FETCH_ASSOC);
    
    // 获取最近30天的使用情况
    $recentUsage = $pdo->query("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as request_count,
            SUM(input_tokens) as daily_input_tokens,
            SUM(output_tokens) as daily_output_tokens
        FROM resume_analysis_results 
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取总请求数
    $totalRequests = $pdo->query("SELECT COUNT(*) as count FROM resume_analysis_results")->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    die("数据库连接失败: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token使用统计</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; color: #007cba; }
        .stat-label { color: #666; margin-top: 5px; }
        .table-container { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: bold; }
        .logout { float: right; color: #dc3545; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 AI简历优化助手 - Token使用统计</h1>
            <a href="?logout=1" class="logout">退出登录</a>
            <?php if (isset($_GET['logout'])): setcookie('admin_auth', '', time()-3600); header('Location: token_stats.php'); endif; ?>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= number_format($totalStats['total_input_tokens'] ?? 0) ?></div>
                <div class="stat-label">总输入Token数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= number_format($totalStats['total_output_tokens'] ?? 0) ?></div>
                <div class="stat-label">总输出Token数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= number_format(($totalStats['total_input_tokens'] ?? 0) + ($totalStats['total_output_tokens'] ?? 0)) ?></div>
                <div class="stat-label">总Token使用量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= number_format($totalRequests['count']) ?></div>
                <div class="stat-label">总分析请求数</div>
            </div>
        </div>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>请求次数</th>
                        <th>输入Token</th>
                        <th>输出Token</th>
                        <th>总Token</th>
                        <th>平均输入Token</th>
                        <th>平均输出Token</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentUsage as $day): ?>
                    <tr>
                        <td><?= $day['date'] ?></td>
                        <td><?= $day['request_count'] ?></td>
                        <td><?= number_format($day['daily_input_tokens']) ?></td>
                        <td><?= number_format($day['daily_output_tokens']) ?></td>
                        <td><?= number_format($day['daily_input_tokens'] + $day['daily_output_tokens']) ?></td>
                        <td><?= $day['request_count'] > 0 ? number_format($day['daily_input_tokens'] / $day['request_count'], 1) : '0' ?></td>
                        <td><?= $day['request_count'] > 0 ? number_format($day['daily_output_tokens'] / $day['request_count'], 1) : '0' ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <?php if (empty($recentUsage)): ?>
                    <tr>
                        <td colspan="7" style="text-align: center; color: #666;">暂无数据</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <div style="margin-top: 20px; text-align: center; color: #666;">
            <p>最后更新时间: <?= $totalStats['last_updated'] ?? '未知' ?></p>
            <p><a href="../index.html">返回主页</a></p>
        </div>
    </div>
</body>
</html>
