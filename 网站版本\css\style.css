/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #ffffff;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo h1 {
    font-size: 1.5rem;
    color: #000000;
    font-weight: 700;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav a:hover {
    color: #000000;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    text-align: center;
    color: #333333;
    background: #f8f9fa;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    color: #000000;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    color: #666666;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #ffffff;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-item .icon {
    font-size: 1.2rem;
}

.cta-button {
    display: inline-block;
    background: #000000;
    color: white;
    padding: 1rem 2.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background: #333333;
    transform: translateY(-2px);
}

/* Sections */
.features, .upload-section, .about {
    padding: 80px 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
}

.section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.feature-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Upload Section */
.upload-section {
    background: #ffffff;
}

.upload-form {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
}

.file-label {
    cursor: pointer;
}

.file-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.file-upload-area:hover {
    border-color: #000000;
    background: #f8f9fa;
}

.file-upload-area.dragover {
    border-color: #000000;
    background: #f1f3f4;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.upload-text strong {
    display: block;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #374151;
}

.upload-text p {
    color: #6b7280;
    font-size: 0.9rem;
}

#file {
    display: none;
}

.file-info {
    background: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #1e40af;
}

textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.95rem;
    resize: vertical;
    transition: border-color 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #000000;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.submit-btn {
    width: 100%;
    background: #000000;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.submit-btn:hover:not(:disabled) {
    background: #333333;
    transform: translateY(-2px);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results Section */
.results-section {
    padding: 80px 0;
    background: #f8fafc;
}

.result-content {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    line-height: 1.8;
}

.result-content h1, .result-content h2, .result-content h3 {
    color: #1f2937;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.result-content h1 {
    font-size: 2rem;
    border-bottom: 3px solid #000000;
    padding-bottom: 0.5rem;
}

.result-content h2 {
    font-size: 1.5rem;
    color: #000000;
}

.result-content h3 {
    font-size: 1.25rem;
}

.result-content ul, .result-content ol {
    margin-left: 2rem;
    margin-bottom: 1rem;
}

.result-content li {
    margin-bottom: 0.5rem;
}

.result-content blockquote {
    border-left: 4px solid #000000;
    padding-left: 1rem;
    margin: 1rem 0;
    background: #f8fafc;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}

.result-content code {
    background: #f1f5f9;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
}

.result-content pre {
    background: #f8f9fa;
    color: #333333;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1rem 0;
    border: 1px solid #e5e7eb;
    white-space: pre-wrap;
    font-family: inherit;
    line-height: 1.6;
}

.result-content pre code {
    background: none;
    padding: 0;
    color: inherit;
    font-family: inherit;
}

/* About Section */
.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.about-text h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #1f2937;
}

.about-text p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #4f46e5;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.about-features h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #1f2937;
}

.feature-list {
    list-style: none;
}

.feature-list li {
    padding: 0.75rem 0;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.feature-list li:last-child {
    border-bottom: none;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
}

.footer-info h3 {
    margin-bottom: 0.5rem;
    color: #4f46e5;
}

.footer-info p {
    color: #9ca3af;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-links a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #4f46e5;
}

.footer-contact p {
    color: #9ca3af;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

/* Results Page Styles */
.results-page {
    padding: 120px 0 80px;
    background: #ffffff;
    min-height: 100vh;
}

.results-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.1rem;
    color: #666666;
    max-width: 600px;
    margin: 0 auto;
}

.loading-container {
    text-align: center;
    padding: 4rem 2rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #000000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.error-container {
    text-align: center;
    padding: 4rem 2rem;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.error-container h3 {
    color: #dc3545;
    margin-bottom: 1rem;
}

.retry-button {
    display: inline-block;
    background: #000000;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    margin-top: 1rem;
    transition: background 0.3s ease;
}

.retry-button:hover {
    background: #333333;
}

.result-meta {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.meta-label {
    font-size: 0.9rem;
    color: #666666;
    font-weight: 500;
}

.result-actions {
    margin-top: 3rem;
    text-align: center;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-button {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.print-button {
    background: #ffffff;
    color: #000000;
    border: 1px solid #000000;
}

.print-button:hover {
    background: #000000;
    color: #ffffff;
}

.new-analysis-button {
    background: #000000;
    color: #ffffff;
}

.new-analysis-button:hover {
    background: #333333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-features {
        flex-direction: column;
        align-items: center;
    }
    
    .nav {
        display: none;
    }
    
    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .stats {
        justify-content: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .container {
        padding: 0 15px;
    }
    
    .upload-form {
        padding: 1.5rem;
    }
}
