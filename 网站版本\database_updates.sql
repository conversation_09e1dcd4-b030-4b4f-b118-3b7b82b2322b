-- 数据库更新脚本：添加Token统计支持

-- 1. 为现有的 resume_analysis_results 表添加Token字段
ALTER TABLE resume_analysis_results 
ADD COLUMN input_tokens INT DEFAULT 0 COMMENT '输入Token数量',
ADD COLUMN output_tokens INT DEFAULT 0 COMMENT '输出Token数量';

-- 2. 创建Token统计表
CREATE TABLE IF NOT EXISTS token_stats (
    id INT PRIMARY KEY DEFAULT 1,
    total_input_tokens BIGINT DEFAULT 0 COMMENT '总输入Token数量',
    total_output_tokens BIGINT DEFAULT 0 COMMENT '总输出Token数量',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) COMMENT='Token使用统计表';

-- 3. 插入初始统计记录
INSERT IGNORE INTO token_stats (id, total_input_tokens, total_output_tokens) 
VALUES (1, 0, 0);

-- 4. 创建Token使用历史表（可选，用于详细统计）
CREATE TABLE IF NOT EXISTS token_usage_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL COMMENT '日期',
    input_tokens INT DEFAULT 0 COMMENT '当日输入Token数量',
    output_tokens INT DEFAULT 0 COMMENT '当日输出Token数量',
    request_count INT DEFAULT 0 COMMENT '当日请求次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date (date)
) COMMENT='Token使用历史记录表';

-- 5. 创建视图：Token使用统计
CREATE OR REPLACE VIEW token_usage_summary AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as request_count,
    SUM(input_tokens) as daily_input_tokens,
    SUM(output_tokens) as daily_output_tokens,
    AVG(input_tokens) as avg_input_tokens,
    AVG(output_tokens) as avg_output_tokens
FROM resume_analysis_results 
WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 6. 创建存储过程：获取Token统计信息
DELIMITER //
CREATE PROCEDURE GetTokenStats()
BEGIN
    SELECT 
        total_input_tokens,
        total_output_tokens,
        (total_input_tokens + total_output_tokens) as total_tokens,
        last_updated
    FROM token_stats 
    WHERE id = 1;
    
    SELECT 
        COUNT(*) as total_requests,
        SUM(input_tokens) as total_input_tokens_from_records,
        SUM(output_tokens) as total_output_tokens_from_records,
        AVG(input_tokens) as avg_input_tokens,
        AVG(output_tokens) as avg_output_tokens,
        MIN(created_at) as first_request,
        MAX(created_at) as last_request
    FROM resume_analysis_results;
    
    SELECT * FROM token_usage_summary LIMIT 7;
END //
DELIMITER ;

-- 使用示例：
-- CALL GetTokenStats();
