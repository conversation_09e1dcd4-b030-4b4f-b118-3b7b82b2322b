-- 简历分析数据库初始化脚本
-- 数据库名称: aakke<PERSON>o_resume_analysis
-- 数据库账号: aak<PERSON><PERSON><PERSON>_shaw

-- 使用数据库
USE `aakkeeco_resume_analysis`;

-- 创建简历分析结果表
CREATE TABLE IF NOT EXISTS `resume_analysis_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件类型',
  `has_jd` tinyint(1) DEFAULT 0 COMMENT '是否包含JD描述',
  `raw_response` longtext COMMENT 'AI原始响应',
  `formatted_text` longtext COMMENT '格式化文本',
  `analysis_data` longtext COMMENT '结构化分析数据(JSON)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_has_jd` (`has_jd`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历分析结果表';

-- 插入示例数据（可选）
-- INSERT INTO `resume_analysis_results` 
-- (`file_name`, `file_type`, `has_jd`, `raw_response`, `formatted_text`, `analysis_data`, `ip_address`) 
-- VALUES 
-- ('示例简历.pdf', 'application/pdf', 1, '示例AI分析结果...', '示例格式化文本...', '{}', '127.0.0.1');

-- 显示表结构
DESCRIBE `resume_analysis_results`;
