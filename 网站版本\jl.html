<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历中转API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        input[type="file"]:focus, textarea:focus {
            outline: none;
            border-color: #007bff;
        }
        textarea {
            height: 120px;
            resize: vertical;
            font-family: inherit;
        }
        button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            width: 100%;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 500px;
            overflow-y: auto;
            border-left: 4px solid;
        }
        .success {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #e2e3e5;
            border-color: #6c757d;
            color: #383d41;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .file-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 12px;
            color: #6c757d;
        }
        .api-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .api-info h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 简历中转API直接测试</h1>
        
        <div class="api-info">
            <h3>📡 API信息</h3>
            <p><strong>目标地址:</strong> https://www.furrywoo.com/jianli/jianli.php</p>
            <p><strong>功能:</strong> 直接调用Gemini API进行简历分析</p>
            <p><strong>支持格式:</strong> PDF, DOC, DOCX, JPG, PNG</p>
            <p><strong>更新内容:</strong> ✅ 增加日志记录 ✅ 提升token限制 ✅ 优化错误处理</p>
        </div>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="file">📄 选择简历文件:</label>
                <input type="file" id="file" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>
            
            <div class="form-group">
                <label for="jd">💼 JD岗位描述 (可选):</label>
                <textarea id="jd" name="jd" placeholder="请粘贴您要应聘的岗位描述(JD)，这将帮助AI更精准地分析简历匹配度...

例如：
职位：高级前端工程师
要求：
- 3年以上前端开发经验
- 熟练掌握React、Vue等框架
- 具备良好的代码规范和团队协作能力"></textarea>
            </div>
            
            <button type="submit" id="submitBtn">🔍 开始AI分析</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        // 文件选择事件
        document.getElementById('file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileInfo = document.getElementById('fileInfo');
            
            if (file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                const fileType = file.type || '未知类型';
                fileInfo.innerHTML = `
                    <strong>文件名:</strong> ${file.name}<br>
                    <strong>大小:</strong> ${fileSize} MB<br>
                    <strong>类型:</strong> ${fileType}
                `;
                fileInfo.style.display = 'block';
            } else {
                fileInfo.style.display = 'none';
            }
        });

        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const result = document.getElementById('result');
            
            // 获取表单数据
            const formData = new FormData();
            const fileInput = document.getElementById('file');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('❌ 请选择一个简历文件', 'error');
                return;
            }
            
            // 检查文件大小 (100MB限制)
            if (file.size > 100 * 1024 * 1024) {
                showResult('❌ 文件太大，请选择小于100MB的文件', 'error');
                return;
            }
            
            formData.append('file', file);
            const jdValue = document.getElementById('jd').value.trim();
            if (jdValue) {
                formData.append('jd', jdValue);
            }
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner"></span>AI分析中...';
            showResult('🤖 正在调用Gemini AI进行简历分析，请耐心等待...\n\n这可能需要30秒到2分钟的时间。', 'loading');
            
            const startTime = Date.now();
            
            try {
                // 直接调用中转API
                const response = await fetch('https://www.furrywoo.com/jianli/jianli.php', {
                    method: 'POST',
                    body: formData
                });
                
                const endTime = Date.now();
                const duration = ((endTime - startTime) / 1000).toFixed(1);
                
                const responseText = await response.text();
                
                if (response.ok) {
                    try {
                        const jsonResult = JSON.parse(responseText);
                        
                        let displayResult = `✅ AI分析完成！(耗时: ${duration}秒)\n\n`;
                        
                        if (jsonResult.success) {
                            displayResult += `📊 分析结果:\n`;
                            displayResult += `文件名: ${jsonResult.file_info?.name || '未知'}\n`;
                            displayResult += `是否包含JD: ${jsonResult.file_info?.has_jd ? '是' : '否'}\n\n`;
                            
                            if (jsonResult.formatted_text) {
                                displayResult += `📝 详细分析:\n${jsonResult.formatted_text}\n\n`;
                            }
                            
                            if (jsonResult.analysis) {
                                displayResult += `🎯 结构化分析:\n${JSON.stringify(jsonResult.analysis, null, 2)}`;
                            }
                        } else {
                            displayResult += `❌ 分析失败: ${jsonResult.error || '未知错误'}`;
                        }
                        
                        showResult(displayResult, 'success');
                    } catch (e) {
                        showResult(`✅ 分析完成！(耗时: ${duration}秒)\n\n📝 原始响应:\n${responseText}`, 'success');
                    }
                } else {
                    let errorMsg = `❌ API调用失败 (HTTP ${response.status})\n\n`;
                    
                    if (response.status === 429) {
                        errorMsg += '🚫 请求频率限制 - 请稍后再试\n';
                        errorMsg += '这通常是因为API调用过于频繁导致的。\n';
                    } else if (response.status === 500) {
                        errorMsg += '🔧 服务器内部错误 - 请检查API配置\n';
                    } else if (response.status === 404) {
                        errorMsg += '🔍 API地址不存在 - 请检查URL配置\n';
                    }
                    
                    errorMsg += `\n📄 响应内容:\n${responseText}`;
                    showResult(errorMsg, 'error');
                }
                
            } catch (error) {
                const endTime = Date.now();
                const duration = ((endTime - startTime) / 1000).toFixed(1);
                
                let errorMsg = `❌ 网络错误 (耗时: ${duration}秒)\n\n`;
                errorMsg += `错误信息: ${error.message}\n\n`;
                
                if (error.message.includes('Failed to fetch')) {
                    errorMsg += '🌐 可能的原因:\n';
                    errorMsg += '1. 网络连接问题\n';
                    errorMsg += '2. API服务器未响应\n';
                    errorMsg += '3. CORS跨域问题\n';
                    errorMsg += '4. API地址不正确\n';
                }
                
                showResult(errorMsg, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '🔍 开始AI分析';
            }
        });
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = 'result ' + type;
            result.scrollTop = 0; // 滚动到顶部
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            showResult('📋 请选择简历文件开始测试\n\n💡 提示: 建议先测试小文件(如图片格式的简历)以验证API连通性', 'info');
        });
    </script>
</body>
</html>
