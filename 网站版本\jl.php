<?php
// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS, GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Max-Age: 86400');

// 数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'resume_analysis',
    'charset' => 'utf8mb4'
];

// 数据库连接函数
function getDbConnection($config) {
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        writeLog("数据库连接失败: " . $e->getMessage(), 'ERROR');
        return null;
    }
}

// 保存分析结果到数据库
function saveAnalysisResult($pdo, $data) {
    try {
        $sql = "INSERT INTO resume_analysis_results (
            file_name,
            file_type,
            has_jd,
            raw_response,
            formatted_text,
            analysis_data,
            created_at,
            ip_address
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)";

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $data['file_info']['name'],
            $data['file_info']['type'],
            $data['file_info']['has_jd'] ? 1 : 0,
            $data['raw_response'],
            $data['formatted_text'],
            json_encode($data['analysis']),
            $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR']
        ]);

        if ($result) {
            $insertId = $pdo->lastInsertId();
            writeLog("分析结果已保存到数据库，ID: " . $insertId);
            return $insertId;
        } else {
            writeLog("保存分析结果失败", 'ERROR');
            return false;
        }
    } catch (PDOException $e) {
        writeLog("保存分析结果时数据库错误: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

// 日志记录函数
function writeLog($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp][$level] $message\n";

    // 写入日志文件
    $logFile = 'jianli_api.log';
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

    // 同时输出到错误日志
    error_log($logMessage);
}

// 记录请求开始
writeLog("接收到请求: " . $_SERVER['REQUEST_METHOD'] . " " . $_SERVER['REQUEST_URI']);
writeLog("客户端IP: " . ($_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR']));

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    writeLog("处理OPTIONS预检请求");
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    writeLog("错误: 不支持的请求方法 " . $_SERVER['REQUEST_METHOD'], 'ERROR');
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit;
}

// 设置Gemini API密钥和模型
$apiKey = 'AIzaSyCkt3alFLjYSjG1nkYeM3UuXHktr3NMPKQ'; // 替换为您的实际API密钥
$model = 'gemini-2.5-flash'; // 使用更稳定的模型

// 构建Gemini API URL
$url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$apiKey}";

// 初始化请求数据
$geminiRequestData = [];

// 处理文件上传
$file_data = null;
$mime_type = null;
$file_content = '';

// 检查是否有文件上传
if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
    // 从上传的文件获取数据
    $file_path = $_FILES['file']['tmp_name'];
    $file_name = $_FILES['file']['name'];
    $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
    $mime_type = $_FILES['file']['type'];

    writeLog("接收到文件上传: $file_name, 类型: $file_extension, 大小: " . $_FILES['file']['size'] . " 字节, MIME: $mime_type");
    
    // 根据文件类型处理
    if (in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
        // 图片文件，直接读取为base64
        $file_data = file_get_contents($file_path);
    } elseif (in_array($file_extension, ['pdf', 'doc', 'docx', 'txt'])) {
        // 文档文件，需要提取文本内容
        $file_content = extractTextFromDocument($file_path, $file_extension);
        if (!$file_content) {
            // 如果文本提取失败，尝试作为图片处理
            $file_data = file_get_contents($file_path);
            $mime_type = 'application/pdf'; // 保持原始MIME类型
        }
    } else {
        writeLog("错误: 不支持的文件格式 $file_extension", 'ERROR');
        http_response_code(400);
        echo json_encode(['error' => '不支持的文件格式']);
        exit;
    }
} elseif (isset($_POST['file_base64'])) {
    // 从POST数据中获取base64编码的文件
    $base64_data = $_POST['file_base64'];
    if (preg_match('/^data:([^;]+);base64,(.+)$/', $base64_data, $matches)) {
        $mime_type = $matches[1];
        $file_data = base64_decode($matches[2]);
    } else {
        $file_data = base64_decode($base64_data);
        $mime_type = 'application/octet-stream';
    }
}

// 如果没有文件数据，返回错误
if (!$file_data && !$file_content) {
    writeLog("错误: 未提供文件数据", 'ERROR');
    http_response_code(400);
    echo json_encode(['error' => 'No file data provided']);
    exit;
}

// 获取JD岗位描述
$jd_description = isset($_POST['jd']) ? $_POST['jd'] : '';
if (!empty($jd_description)) {
    writeLog("接收到JD描述，长度: " . strlen($jd_description) . " 字符");
}

// 构建简历警察V3的完整Prompt
$resume_prompt = buildResumeAnalysisPrompt($jd_description);

// 构建请求内容
$request_parts = [
    [
        'text' => $resume_prompt
    ]
];

// 如果有文本内容，直接添加到prompt中
if ($file_content) {
    $request_parts[0]['text'] .= "\n\n这是我的简历内容：\n```\n" . $file_content . "\n```";
} elseif ($file_data) {
    // 如果是文件数据，添加为内联数据
    $request_parts[] = [
        'inline_data' => [
            'mime_type' => $mime_type,
            'data' => base64_encode($file_data)
        ]
    ];
}

// 如果有JD描述，添加到prompt中
if (!empty($jd_description)) {
    $request_parts[0]['text'] .= "\n\n这是我面试的岗位的JD：\n```\n" . $jd_description . "\n```";
}

// 构建多模态请求
$geminiRequestData = [
    'contents' => [
        [
            'parts' => $request_parts
        ]
    ],
    'generationConfig' => [
        'temperature' => 0.9,
        'topP' => 0.8,
        'topK' => 40,
        'maxOutputTokens' => 15000  // 增加输出token限制
    ]
];

writeLog("开始调用Gemini API: $url");
writeLog("请求数据大小: " . strlen(json_encode($geminiRequestData)) . " 字节");

// 初始化cURL会话
$ch = curl_init($url);

// 设置cURL选项
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($geminiRequestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 180); // 3分钟超时
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时30秒

$start_time = microtime(true);

// 执行cURL请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

$end_time = microtime(true);
$duration = round(($end_time - $start_time) * 1000, 2);

writeLog("Gemini API调用完成，耗时: {$duration}ms, HTTP状态码: $httpCode");

// 关闭cURL会话
curl_close($ch);

// 检查cURL错误
if ($curlError) {
    writeLog("cURL错误: $curlError", 'ERROR');
    http_response_code(500);
    echo json_encode(['error' => 'cURL Error: ' . $curlError]);
    exit;
}

// 检查HTTP状态码
if ($httpCode !== 200) {
    // 记录详细错误信息
    writeLog("Gemini API错误 - HTTP状态码: $httpCode", 'ERROR');
    writeLog("错误响应: " . substr($response, 0, 500), 'ERROR');

    // 根据不同错误码返回友好提示
    $errorMessage = "API调用失败 (HTTP $httpCode)";
    if ($httpCode === 429) {
        $errorMessage = "请求频率限制，请稍后再试";
        writeLog("触发API频率限制", 'WARN');
    } elseif ($httpCode === 401) {
        $errorMessage = "API密钥无效";
        writeLog("API密钥验证失败", 'ERROR');
    } elseif ($httpCode === 403) {
        $errorMessage = "API访问被拒绝";
        writeLog("API访问权限被拒绝", 'ERROR');
    } elseif ($httpCode === 500) {
        $errorMessage = "Gemini服务器内部错误";
        writeLog("Gemini服务器内部错误", 'ERROR');
    }

    http_response_code($httpCode);
    echo json_encode(['error' => $errorMessage, 'details' => $response]);
    exit;
}

// 解析响应
$decoded_response = json_decode($response, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    writeLog("JSON解析失败: " . json_last_error_msg(), 'ERROR');
    writeLog("原始响应: " . substr($response, 0, 1000), 'ERROR');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to parse API response']);
    exit;
}

// 提取AI生成的文本
$ai_text = '';
if (isset($decoded_response['candidates'][0]['content']['parts'][0]['text'])) {
    $ai_text = $decoded_response['candidates'][0]['content']['parts'][0]['text'];
    writeLog("成功提取AI响应，长度: " . strlen($ai_text) . " 字符");
} else {
    writeLog("警告: 无法从API响应中提取文本内容", 'WARN');
    writeLog("响应结构: " . json_encode($decoded_response), 'DEBUG');
}

// 构建返回结果
$result = [
    'success' => true,
    'raw_response' => $ai_text,
    'formatted_text' => $ai_text,
    'analysis' => parseResumeAnalysis($ai_text),
    'file_info' => [
        'name' => isset($file_name) ? $file_name : 'uploaded_file',
        'type' => $mime_type,
        'has_jd' => !empty($jd_description)
    ]
];

writeLog("简历分析完成，返回结果大小: " . strlen(json_encode($result)) . " 字节");

// 尝试将数据回传到本地API保存
$local_api_url = $_POST['callback_url'] ?? null;
if ($local_api_url) {
    writeLog("尝试将数据回传到本地API: " . $local_api_url);
    $callback_success = sendDataToLocalAPI($local_api_url, $result);
    if ($callback_success) {
        writeLog("数据已成功回传到本地API");
        $result['saved_to_local'] = true;
    } else {
        writeLog("回传到本地API失败", 'WARN');
        $result['saved_to_local'] = false;
    }
} else {
    writeLog("未提供本地API回调地址，跳过数据回传");
    $result['saved_to_local'] = false;
}

writeLog("处理完成，总耗时: " . round((microtime(true) - $start_time) * 1000, 2) . "ms");

// 返回结果
echo json_encode($result);

/**
 * 将数据发送到本地API保存
 */
function sendDataToLocalAPI($callback_url, $data) {
    try {
        $postData = [
            'action' => 'save_analysis_result',
            'data' => $data,
            'timestamp' => time(),
            'source' => 'gemini_api'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $callback_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'User-Agent: GeminiAPI/1.0'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            writeLog("回传数据时CURL错误: " . $error, 'ERROR');
            return false;
        }

        if ($httpCode !== 200) {
            writeLog("回传数据时HTTP错误: " . $httpCode, 'ERROR');
            writeLog("响应内容: " . substr($response, 0, 500), 'ERROR');
            return false;
        }

        $responseData = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            writeLog("回传响应JSON解析失败: " . json_last_error_msg(), 'ERROR');
            return false;
        }

        if (isset($responseData['success']) && $responseData['success']) {
            writeLog("数据成功回传到本地API，保存ID: " . ($responseData['saved_id'] ?? 'unknown'));
            return true;
        } else {
            writeLog("本地API返回失败: " . ($responseData['error'] ?? 'unknown error'), 'ERROR');
            return false;
        }

    } catch (Exception $e) {
        writeLog("回传数据时异常: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

/**
 * 构建简历分析Prompt
 */
function buildResumeAnalysisPrompt($jd_description = '') {
    $prompt = '#  **【角色】洞察人心的面试官与资深HRBP (v2.0)**

你是一位顶尖科技公司（FAANG级别）的技术招聘委员会核心成员，兼具技术Leader的深度、资深HRBP的广度和增长思维教练（Growth Coach）的启发性。你以"一针见血的批判"和"点石成金的建议"在业内闻名。你的使命是三重的：不仅要像代码审查（Code Review）一样无情地审计简历中的每一个瑕疵，还要像导师（Mentor）一样，为候选人提供一套清晰、可行、能从根本上提升其职业竞争力的修改蓝图，并最终像战略家（Strategist）一样，帮助候选人构建一个引人入胜的职业故事。

# **核心原则与规则 (Core Principles & Rules):**

1. **内容为王，格式为辅 (Content First, Format Second):** 我将假设文本的排版可能因从PDF复制而失真，因此我会专注于内容本身。但是，任何**拼写、语法、标点和专业术语**的错误都将被视为不可原谅的硬伤，因为这直接反映了候选人的严谨性。

2. **岗位简历匹配原则:** 你不能用锤子的要求看钉子，也不能用钉子的要求看锤子。如果用户提供了目标岗位的JD，运用你的经验分析JD的需求与用户简历，不是所有的简历都是要投递给FAANG级别的公司。

3. **"所以呢？"拷问法 (The "So What?" Test):** 对简历中的每一句陈述，都在内心进行"所以呢？"的拷问。如果一句描述无法回答"它带来了什么具体价值或影响？"，那么它就是无效信息。

4. **"批判-解析-建议"三位一体模型 (The "Critique-Analysis-Suggestion" Trinity):** 这是你所有反馈的**唯一**格式。对于发现的每一个问题，你都必须：
   - ❓ **清晰地指出问题 (Critique):** 直截了当地点出弱点。
   - 🤔 **解释负面影响 (Analysis):** 解释这个问题会如何让招聘经理/面试官产生负面联想。
   - 💡 **给出具体方案 (Suggestion):** 给出可操作的修改方案、叙事工具或启发性问题，引导候选人挖掘更深层次的信息。

5. **分级批判 (Tiered Critique):** 根据你判断的候选人目标级别以及岗位JD（例如：初级、高级、专家），调整你的批判标准和期望值。对高级候选人，你应更苛求其在**架构设计、技术决策、领导力和业务影响力**上的体现。如果没有提供岗位JD，应该根据经验/项目/学习能力进行评级，进而进行批判。

6. **技术审判官 (Technical Judge):** 作为技术负责人，你必须对简历中的每一个技术细节进行批判性审视。任何技术上的模糊描述、错误的术语使用或不切实际的夸大其词等等问题，都必须被指出来。

# **工作流程 (Workflow):**

严格遵循以下五步流程：

### **Step 1: 第一印象与初步诊断 (First Impression & Initial Diagnosis)**

1. **目标定位判断**: 基于简历内容(如果有JD，也应该参考JD)，快速判断候选人可能的目标岗位和职级（例如：后端开发-高级，数据科学-初级）。
2. **30秒定论**: 给出你作为招聘官的第一印象，直截了当地说出这份简历是"**留下深入研究**"还是"**大概率关闭**"，并用一句话说明核心原因。

### **Step 2: 地毯式深度审计与指导 (Line-by-Line Audit & Mentorship)**

> 这是最核心的步骤。你将对简历进行自上而下的、地毯式的审计。**对于每一个审计项发现的问题，你都必须严格遵循"批判-解析-建议"三位一体模型进行反馈。**

#### **A. 整体审计 (Holistic Audit):**

- [ ] **职业故事线 (Career Narrative):**
  - ❓ 职业路径是否清晰连贯？每一步跳槽或项目选择的逻辑是什么？是否存在断层或不合理的转变？是否存在外包公司(中科软/中软国际/法本/国通/洛道/华为OD/软通动力...)?
  - 🤔 例如: 混乱的路径让我怀疑你的职业规划能力和长期稳定性。
  - 💡 如果路径不寻常，请在个人摘要中用一句话主动解释其背后的逻辑，化被动为主动。

- [ ] **关键词与技术栈匹配度 (Keyword & Tech Stack Alignment):**
  - ❓ 简历中的技术关键词和项目经验，是否与第一步判断的目标岗位高度匹配？
  - 🤔 例如: 如果我想招一个Go的后端，但你简历里全是Java，我可能一开始就不会往下看。
  - 💡 指出需要根据目标岗位JD，微调你的技能列表和项目描述，突出最相关的技术栈。

- [ ] **一致性检查 (Consistency Check):**
  - ❓ 不同项目描述中使用的技术、数据或角色是否存在逻辑矛盾？
  - 🤔 例如:一个小小的矛盾就会让我质疑你所有经历的真实性。
  - 💡 通读全文，确保所有信息（如工作年限、技术栈版本、团队规模）都是一致的。

- [ ] **无效内容过滤 (Noise Filtering):**
  - ❓ 是否存在毫无价值的"玩具项目"（如无用户、无真实场景的课程作业、烂大街的XX外卖/秒杀平台）？
  - 🤔 看到这些项目，我会认为你缺乏真实世界的工程经验，只能用这些来凑数。
  - 💡 与其放一个平庸的玩具项目，不如深入挖掘你工作中最有挑战性的一个技术细节。

#### **B. 模块化审计 (Section-by-Section Audit):**

- **[ ] 个人摘要/简介 (Summary/Objective):**
  - ❓ 是否超过三行？是否包含了"热情"、"努力"等主观、空洞的词汇？是否清晰概括了你的核心竞争力？
  - 🤔 一个糟糕的开场白，让我没有耐心看下去。
  - 💡 使用公式：`[你的定位] + [工作年限] + [核心技术领域] + [最亮眼的一项成就]`。

- **[ ] 工作/项目经历 (Work/Project Experience) - 对每一段经历进行独立审计:**
  - **对每一条 bullet point，运用以下清单进行拷问，并始终使用"批判-解析-建议"模型反馈：**
    - [ ] **叙事框架的完整性 (Narrative Framework):** 描述是否遵循了清晰的逻辑（如STAR, CAR, PAR）？`Result`是否缺失或模糊？
    - [ ] **"所以呢？"拷问的深度**: 这条描述的最终价值是什么？对业务、技术或团队有何具体影响？
    - [ ] **技术洞察与决策 (Technical Insight & Decision):** 描述是停留在"使用了XX技术"，还是深入到了"**为解决[什么问题]**，我在[方案A]和[方案B]之间进行了**权衡**，最终选择[方案X]，并**通过[关键实现细节]** 达成了目标"？
    - [ ] **动词的力量 (Power Verbs):** 动词是强有力的（如Architected, Led, Optimized, Reduced）还是软弱的（如Involved in, Responsible for, Assisted）？
    - [ ] **影响力的证明 (Evidence of Impact):** 是否包含了**影响力证明**？如果无法直接**量化**（百分比、具体数字），是否使用了**定性成果**、**范围规模**、**战略价值**或**风险规避**来证明？
    - [ ] **影响力的层级 (Scope of Influence):** 成果的影响力是局限于个人，还是扩展到了团队、部门乃至公司层面？
    - [ ] **隐性软技能展示 (Implicit Soft Skills Showcase):** 描述中是否通过实际行动展现了软技能？

- **[ ] 技术技能 (Skills):**
  - ❓ 技能的熟练度（如"精通"、"熟悉"）是否在项目中得到了印证？
  - 🤔 技能与项目脱节，会让我严重怀疑你的诚信和实际能力。
  - 💡 确保你列出的每一项"精通"或"熟悉"的技能，都能在项目经历中找到强有力的支撑案例。

  - [ ] **技术前瞻性与学习能力 (Tech Foresight & Learning Aptitude):**
  - ❓ 在AI浪潮下，是否体现了利用AI工具提效或探索业务结合的意识？
  - 🤔 对技术演进完全无感，可能会被认为技术视野狭隘，学习能力滞后。
  - 💡 如果你有使用Copilot、ChatGPT等工具提升开发效率，或在项目中探索了AIGC的应用，请务必加上。

### **Step 3: 战略性修改蓝图 (Strategic Revision Blueprint)**

提供一个清晰、可执行的修改计划。

1. **影响力叙事工具箱 (Impact Narrative Toolbox):** 明确指导如何将"职责描述"改写为"成就描述"。提供黄金公式**工具箱**，并指导何时使用：
   - **基础公式 (STAR/CAR):** "为了[业务目标/技术挑战] (Situation/Task/Challenge)，我[采取的关键行动，体现技术深度] (Action)，最终带来了[可量化的/可感知的成果] (Result)"。
   - **进阶公式 (决策-权衡):** "为解决[复杂问题]，我们评估了[方案A]和[方案B]。我主张选择[方案A]，因为[关键理由]，并设计了[配套措施]来规避其[风险]，最终[达成的战略成果]。"

2. **挖掘隐藏亮点的启发式提问 (Heuristic Questions):** 引导候选人进行更深层次的思考。

3. **影响力思维训练 (Impact Thinking Training):** 指导候选人如何将看似无法量化的工作具象化。

### **Step 4: 重构与展示：修改后的简历范本 (Restructure & Showcase: The Revised Resume Template)**

基于以上所有分析，生成一份完整的、使用Markdown格式的修改后简历范本。

- **规则1：忠于原文信息**：绝不凭空捏造事实。
- **规则2：展示最佳实践**：将所有描述都按照"影响力叙事工具箱"进行改写。
- **规则3：植入"启发式占位符"** : 对于原文缺失的关键信息，使用明确且带有引导性的占位符。

### **Step 5: 最终裁决与行动清单 (Final Verdict & Action Items)**

给出最后的、决定性的评语。

1. **整体评价**: 对比修改前后的简历，用简短的话语总结其核心提升点。
2. **核心风险点**: 再次强调原始简历中最致命的问题。
3. **下一步行动清单 (Action List)** : 给出清晰的下一步行动项。

**语言要求**：
- 必须使用简体中文回答，禁止使用英文单词或短语
- 所有专业术语、技术名词都必须使用中文表达
- 使用Emoji进行更好的视觉提醒，注意你的输出排版应该做到清晰明了
- 使用Markdown格式进行排版，包括标题(#)、加粗(**)、列表(-)等

**内容要求**：
1. 请提供完整详细的分析，不要因为篇幅限制而省略内容
2. 确保所有5个步骤都有详细的内容输出
3. 特别是Step 4的修改后简历范本，请尽可能提供具体的修改建议和示例
4. 如果内容较长，请优先保证分析的完整性和实用性
5. 使用Markdown格式美化排版，让内容更易读

**格式要求**：
- 使用 # ## ### 标记标题层级
- 使用 **文本** 标记重要内容
- 使用 - 标记列表项
- 保持清晰的段落结构

当前时间: 2025-07-28 00:00，请严格按照这个时间对简历中出现的时间进行判断。';

    return $prompt;
}

/**
 * 解析简历分析结果
 */
function parseResumeAnalysis($ai_text) {
    // 尝试从AI文本中提取结构化信息
    $analysis = [
        'firstImpression' => '',
        'targetPosition' => '',
        'overallAssessment' => '',
        'keyStrengths' => [],
        'majorWeaknesses' => [],
        'improvementSuggestions' => [],
        'revisedResume' => '',
        'actionItems' => []
    ];
    
    // 简单的文本解析逻辑
    $lines = explode("\n", $ai_text);
    $current_section = '';
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // 检测章节标题
        if (strpos($line, 'Step 1') !== false || strpos($line, '第一印象') !== false) {
            $current_section = 'firstImpression';
        } elseif (strpos($line, 'Step 2') !== false || strpos($line, '深度审计') !== false) {
            $current_section = 'audit';
        } elseif (strpos($line, 'Step 3') !== false || strpos($line, '修改蓝图') !== false) {
            $current_section = 'blueprint';
        } elseif (strpos($line, 'Step 4') !== false || strpos($line, '修改后') !== false) {
            $current_section = 'revised';
        } elseif (strpos($line, 'Step 5') !== false || strpos($line, '行动清单') !== false) {
            $current_section = 'actions';
        }
        
        // 根据当前章节收集信息
        switch ($current_section) {
            case 'firstImpression':
                if (strpos($line, '目标岗位') !== false || strpos($line, '目标定位') !== false) {
                    $analysis['targetPosition'] .= $line . "\n";
                } else {
                    $analysis['firstImpression'] .= $line . "\n";
                }
                break;
            case 'revised':
                $analysis['revisedResume'] .= $line . "\n";
                break;
            case 'actions':
                if (strpos($line, '任务') !== false || strpos($line, '建议') !== false) {
                    $analysis['actionItems'][] = $line;
                }
                break;
        }
    }
    
    // 清理数据
    $analysis['firstImpression'] = trim($analysis['firstImpression']);
    $analysis['targetPosition'] = trim($analysis['targetPosition']);
    $analysis['revisedResume'] = trim($analysis['revisedResume']);
    
    return $analysis;
}

/**
 * 从文档中提取文本内容
 */
function extractTextFromDocument($file_path, $extension) {
    switch ($extension) {
        case 'txt':
            return file_get_contents($file_path);
            
        case 'pdf':
            // 这里需要PDF解析库，暂时返回空
            // 可以使用 pdftotext 命令行工具或PHP PDF库
            return '';
            
        case 'doc':
        case 'docx':
            // 这里需要Word文档解析库，暂时返回空
            // 可以使用 PHPWord 或其他库
            return '';
            
        default:
            return '';
    }
}
?>
