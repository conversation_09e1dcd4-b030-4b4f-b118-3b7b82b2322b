// 国际化语言包
const i18n = {
    // 英语 (默认)
    'en': {
        // Header
        'site_title': '🎯 AI Resume Optimizer',
        'nav_features': 'Features',
        'nav_analyze': 'Start Analysis',
        'nav_about': 'About Us',
        
        // Hero Section
        'hero_title': 'Let AI Enhance Your Resume',
        'hero_subtitle': 'Professional resume analysis and optimization suggestions to help you stand out in your job search',
        'hero_ai_analysis': 'AI Smart Analysis',
        'hero_multi_format': 'Multi-format Support',
        'hero_fast_generation': 'Fast Generation',
        'hero_professional_advice': 'Professional Advice',
        'hero_cta': 'Start Analysis Now',
        
        // Features Section
        'features_title': 'Why Choose Our AI Resume Analysis',
        'feature_precise_title': 'Precise Analysis',
        'feature_precise_desc': 'Based on the latest AI technology, deeply analyze your resume content, identify strengths and weaknesses, and provide targeted improvement suggestions',
        'feature_matching_title': 'Job Matching',
        'feature_matching_desc': 'Combined with target job descriptions, analyze resume-position matching to help you highlight relevant skills and experience',
        'feature_optimization_title': 'Professional Optimization',
        'feature_optimization_desc': 'Provide specific modification suggestions and optimization plans, including content structure, keyword optimization, expression methods, etc.',
        'feature_convenient_title': 'Fast & Convenient',
        'feature_convenient_desc': 'Support PDF, Word, images and other formats, upload to get detailed analysis reports',
        
        // Upload Section
        'upload_title': 'Start Analyzing Your Resume',
        'upload_subtitle': 'Upload your resume file, our AI will provide professional analysis and suggestions',
        'upload_area_text': 'Click to select file or drag here',
        'upload_area_desc': 'Support PDF, DOC, DOCX, JPG, PNG formats, max 100MB',
        'jd_label': '💼 Target Job Description (Optional)',
        'jd_placeholder': 'Please paste the job description (JD) you are applying for, this will help AI provide more accurate matching analysis...\n\nExample:\nPosition: Senior Frontend Engineer\nRequirements:\n- 3+ years frontend development experience\n- Proficient in React, Vue frameworks\n- Good coding standards and teamwork skills\n- Mobile development experience preferred',
        'submit_button': '🔍 Start AI Analysis',
        'submit_loading': 'Analyzing...',
        
        // About Section
        'about_title': 'About Us',
        'about_service_title': 'Professional AI Resume Analysis Service',
        'about_service_desc1': 'We are committed to providing job seekers with the most professional resume analysis and optimization services. Through advanced AI technology, we can deeply analyze resume content, identify strengths and weaknesses, and provide specific improvement suggestions.',
        'about_service_desc2': 'Whether you are a fresh graduate or an experienced professional, we can provide personalized resume optimization solutions to help you stand out in fierce job competition.',
        'about_advantages_title': 'Our Advantages',
        'about_advantage_1': '✅ Deep analysis based on latest AI technology',
        'about_advantage_2': '✅ Support multiple file format uploads',
        'about_advantage_3': '✅ Precise matching analysis combined with job JD',
        'about_advantage_4': '✅ Detailed optimization suggestions and modification plans',
        'about_advantage_5': '✅ Fast response, instant results',
        'about_advantage_6': '✅ Data security, privacy protection',
        'stat_analyzed': 'Resumes Analyzed',
        'stat_satisfaction': 'User Satisfaction',
        'stat_service': 'Online Service',
        
        // Footer
        'footer_tagline': 'Let AI empower your career development',
        'footer_contact': 'Contact us: <EMAIL>',
        'footer_copyright': '© 2025 AI Resume Optimizer. All rights reserved.',
        
        // Notifications
        'error_no_file': 'Please select a resume file',
        'error_file_too_large': 'File too large, please select a file smaller than 100MB',
        'error_unsupported_format': 'Unsupported file format, please select PDF, DOC, DOCX, JPG, PNG or TXT files',
        'analyzing_message': 'Calling AI for resume analysis, please wait...',
        'analysis_complete': 'AI analysis completed! (Time: {duration}s)',
        'analysis_failed': 'Analysis failed: {error}',
        'api_error': 'API call failed (HTTP {status})',
        'network_error': 'Network error (Time: {duration}s): {message}',
        
        // Result Page
        'result_title': '📊 Resume Analysis Results',
        'result_subtitle': 'Professional resume analysis report based on AI technology',
        'result_loading': 'Loading analysis results...',
        'result_error_title': 'Loading Failed',
        'result_error_message': 'Unable to load analysis results, please try again.',
        'result_return_home': 'Return to Homepage for Re-analysis',
        'result_file_name': 'File Name:',
        'result_analysis_time': 'Analysis Time:',
        'result_has_jd': 'Includes JD:',
        'result_print': '🖨️ Print Report',
        'result_new_analysis': '🔄 New Analysis',
        'yes': 'Yes',
        'no': 'No',
        'no_analysis_result': 'No analysis results available'
    },
    
    // 中文简体
    'zh-CN': {
        'site_title': '🎯 AI简历优化助手',
        'nav_features': '功能特色',
        'nav_analyze': '开始分析',
        'nav_about': '关于我们',
        'hero_title': '让AI为您的简历加分',
        'hero_subtitle': '专业的简历分析与优化建议，助您在求职路上脱颖而出',
        'hero_ai_analysis': 'AI智能分析',
        'hero_multi_format': '多格式支持',
        'hero_fast_generation': '快速生成',
        'hero_professional_advice': '专业建议',
        'hero_cta': '立即开始分析',
        'features_title': '为什么选择我们的AI简历分析',
        'feature_precise_title': '精准分析',
        'feature_precise_desc': '基于最新的AI技术，深度分析您的简历内容，识别优势与不足，提供针对性的改进建议',
        'feature_matching_title': '岗位匹配',
        'feature_matching_desc': '结合目标岗位JD，分析简历与职位的匹配度，帮助您突出相关技能和经验',
        'feature_optimization_title': '专业优化',
        'feature_optimization_desc': '提供具体的修改建议和优化方案，包括内容结构、关键词优化、表达方式等',
        'feature_convenient_title': '快速便捷',
        'feature_convenient_desc': '支持PDF、Word、图片等多种格式，上传即可获得详细的分析报告',
        'upload_title': '开始分析您的简历',
        'upload_subtitle': '上传您的简历文件，我们的AI将为您提供专业的分析和建议',
        'upload_area_text': '点击选择文件或拖拽到此处',
        'upload_area_desc': '支持 PDF、DOC、DOCX、JPG、PNG 格式，最大100MB',
        'jd_label': '💼 目标岗位描述 (可选)',
        'jd_placeholder': '请粘贴您要应聘的岗位描述(JD)，这将帮助AI更精准地分析简历匹配度...\n\n例如：\n职位：高级前端工程师\n要求：\n- 3年以上前端开发经验\n- 熟练掌握React、Vue等框架\n- 具备良好的代码规范和团队协作能力\n- 有移动端开发经验优先',
        'submit_button': '🔍 开始AI分析',
        'submit_loading': '分析中...',
        'about_title': '关于我们',
        'about_service_title': '专业的AI简历分析服务',
        'about_service_desc1': '我们致力于为求职者提供最专业的简历分析和优化服务。通过先进的AI技术，我们能够深度分析简历内容，识别优势与不足，并提供具体的改进建议。',
        'about_service_desc2': '无论您是应届毕业生还是资深职场人士，我们都能为您提供个性化的简历优化方案，帮助您在激烈的求职竞争中脱颖而出。',
        'about_advantages_title': '我们的优势',
        'about_advantage_1': '✅ 基于最新AI技术的深度分析',
        'about_advantage_2': '✅ 支持多种文件格式上传',
        'about_advantage_3': '✅ 结合岗位JD的精准匹配分析',
        'about_advantage_4': '✅ 详细的优化建议和修改方案',
        'about_advantage_5': '✅ 快速响应，即时获得结果',
        'about_advantage_6': '✅ 数据安全，隐私保护',
        'stat_analyzed': '简历已分析',
        'stat_satisfaction': '用户满意度',
        'stat_service': '在线服务',
        'footer_tagline': '让AI为您的职业发展助力',
        'footer_contact': '联系我们: <EMAIL>',
        'footer_copyright': '© 2025 AI简历优化助手. All rights reserved.',
        'error_no_file': '请选择一个简历文件',
        'error_file_too_large': '文件太大，请选择小于100MB的文件',
        'error_unsupported_format': '不支持的文件格式，请选择PDF、DOC、DOCX、JPG、PNG或TXT文件',
        'analyzing_message': '正在调用AI进行简历分析，请耐心等待...',
        'analysis_complete': 'AI分析完成！(耗时: {duration}秒)',
        'analysis_failed': '分析失败: {error}',
        'api_error': 'API调用失败 (HTTP {status})',
        'network_error': '网络错误 (耗时: {duration}秒): {message}',
        'result_title': '📊 简历分析结果',
        'result_subtitle': '基于AI技术的专业简历分析报告',
        'result_loading': '正在加载分析结果...',
        'result_error_title': '加载失败',
        'result_error_message': '无法加载分析结果，请重试。',
        'result_return_home': '返回首页重新分析',
        'result_file_name': '文件名:',
        'result_analysis_time': '分析时间:',
        'result_has_jd': '包含JD:',
        'result_print': '🖨️ 打印报告',
        'result_new_analysis': '🔄 重新分析',
        'yes': '是',
        'no': '否',
        'no_analysis_result': '暂无分析结果'
    },
    
    // 中文繁体
    'zh-TW': {
        'site_title': '🎯 AI履歷優化助手',
        'nav_features': '功能特色',
        'nav_analyze': '開始分析',
        'nav_about': '關於我們',
        'hero_title': '讓AI為您的履歷加分',
        'hero_subtitle': '專業的履歷分析與優化建議，助您在求職路上脫穎而出',
        'hero_ai_analysis': 'AI智能分析',
        'hero_multi_format': '多格式支援',
        'hero_fast_generation': '快速生成',
        'hero_professional_advice': '專業建議',
        'hero_cta': '立即開始分析',
        'features_title': '為什麼選擇我們的AI履歷分析',
        'feature_precise_title': '精準分析',
        'feature_precise_desc': '基於最新的AI技術，深度分析您的履歷內容，識別優勢與不足，提供針對性的改進建議',
        'feature_matching_title': '職位匹配',
        'feature_matching_desc': '結合目標職位JD，分析履歷與職位的匹配度，幫助您突出相關技能和經驗',
        'feature_optimization_title': '專業優化',
        'feature_optimization_desc': '提供具體的修改建議和優化方案，包括內容結構、關鍵詞優化、表達方式等',
        'feature_convenient_title': '快速便捷',
        'feature_convenient_desc': '支援PDF、Word、圖片等多種格式，上傳即可獲得詳細的分析報告',
        'upload_title': '開始分析您的履歷',
        'upload_subtitle': '上傳您的履歷檔案，我們的AI將為您提供專業的分析和建議',
        'upload_area_text': '點擊選擇檔案或拖拽到此處',
        'upload_area_desc': '支援 PDF、DOC、DOCX、JPG、PNG 格式，最大100MB',
        'jd_label': '💼 目標職位描述 (可選)',
        'jd_placeholder': '請貼上您要應聘的職位描述(JD)，這將幫助AI更精準地分析履歷匹配度...',
        'submit_button': '🔍 開始AI分析',
        'submit_loading': '分析中...',
        'about_title': '關於我們',
        'about_service_title': '專業的AI履歷分析服務',
        'about_service_desc1': '我們致力於為求職者提供最專業的履歷分析和優化服務。通過先進的AI技術，我們能夠深度分析履歷內容，識別優勢與不足，並提供具體的改進建議。',
        'about_service_desc2': '無論您是應屆畢業生還是資深職場人士，我們都能為您提供個性化的履歷優化方案，幫助您在激烈的求職競爭中脫穎而出。',
        'about_advantages_title': '我們的優勢',
        'about_advantage_1': '✅ 基於最新AI技術的深度分析',
        'about_advantage_2': '✅ 支援多種檔案格式上傳',
        'about_advantage_3': '✅ 結合職位JD的精準匹配分析',
        'about_advantage_4': '✅ 詳細的優化建議和修改方案',
        'about_advantage_5': '✅ 快速響應，即時獲得結果',
        'about_advantage_6': '✅ 資料安全，隱私保護',
        'stat_analyzed': '履歷已分析',
        'stat_satisfaction': '用戶滿意度',
        'stat_service': '線上服務',
        'footer_tagline': '讓AI為您的職業發展助力',
        'footer_contact': '聯繫我們: <EMAIL>',
        'footer_copyright': '© 2025 AI履歷優化助手. All rights reserved.',
        'error_no_file': '請選擇一個履歷檔案',
        'error_file_too_large': '檔案太大，請選擇小於100MB的檔案',
        'error_unsupported_format': '不支援的檔案格式，請選擇PDF、DOC、DOCX、JPG、PNG或TXT檔案',
        'analyzing_message': '正在調用AI進行履歷分析，請耐心等待...',
        'analysis_complete': 'AI分析完成！(耗時: {duration}秒)',
        'analysis_failed': '分析失敗: {error}',
        'api_error': 'API調用失敗 (HTTP {status})',
        'network_error': '網路錯誤 (耗時: {duration}秒): {message}',
        'result_title': '📊 履歷分析結果',
        'result_subtitle': '基於AI技術的專業履歷分析報告',
        'result_loading': '正在載入分析結果...',
        'result_error_title': '載入失敗',
        'result_error_message': '無法載入分析結果，請重試。',
        'result_return_home': '返回首頁重新分析',
        'result_file_name': '檔案名:',
        'result_analysis_time': '分析時間:',
        'result_has_jd': '包含JD:',
        'result_print': '🖨️ 列印報告',
        'result_new_analysis': '🔄 重新分析',
        'yes': '是',
        'no': '否'
    },

    // 日语
    'ja': {
        'site_title': '🎯 AI履歴書最適化アシスタント',
        'nav_features': '機能',
        'nav_analyze': '分析開始',
        'nav_about': '私たちについて',
        'hero_title': 'AIがあなたの履歴書をレベルアップ',
        'hero_subtitle': 'プロフェッショナルな履歴書分析と最適化提案で、就職活動を成功に導きます',
        'hero_ai_analysis': 'AIスマート分析',
        'hero_multi_format': '多形式対応',
        'hero_fast_generation': '高速生成',
        'hero_professional_advice': 'プロのアドバイス',
        'hero_cta': '今すぐ分析開始',
        'features_title': 'なぜ私たちのAI履歴書分析を選ぶのか',
        'feature_precise_title': '精密分析',
        'feature_precise_desc': '最新のAI技術に基づき、履歴書の内容を深く分析し、強みと弱みを特定し、的確な改善提案を提供します',
        'feature_matching_title': '求人マッチング',
        'feature_matching_desc': '目標求人の職務記述書と組み合わせて、履歴書と職位のマッチング度を分析し、関連スキルと経験を強調します',
        'feature_optimization_title': 'プロの最適化',
        'feature_optimization_desc': '具体的な修正提案と最適化プランを提供し、内容構造、キーワード最適化、表現方法などを含みます',
        'feature_convenient_title': '高速・便利',
        'feature_convenient_desc': 'PDF、Word、画像など多様な形式に対応し、アップロードするだけで詳細な分析レポートを取得できます',
        'upload_title': 'あなたの履歴書の分析を開始',
        'upload_subtitle': '履歴書ファイルをアップロードすると、私たちのAIがプロフェッショナルな分析と提案を提供します',
        'upload_area_text': 'ファイルを選択またはここにドラッグ',
        'upload_area_desc': 'PDF、DOC、DOCX、JPG、PNG形式対応、最大100MB',
        'jd_label': '💼 目標求人記述（オプション）',
        'jd_placeholder': '応募する求人記述（JD）を貼り付けてください。AIがより正確なマッチング分析を提供します...',
        'submit_button': '🔍 AI分析開始',
        'submit_loading': '分析中...',
        'about_title': '私たちについて',
        'about_service_title': 'プロフェッショナルなAI履歴書分析サービス',
        'about_service_desc1': '私たちは求職者に最もプロフェッショナルな履歴書分析と最適化サービスを提供することに専念しています。先進的なAI技術により、履歴書の内容を深く分析し、強みと弱みを特定し、具体的な改善提案を提供します。',
        'about_service_desc2': '新卒者でも経験豊富な職業人でも、個人に合わせた履歴書最適化ソリューションを提供し、激しい就職競争で際立つお手伝いをします。',
        'about_advantages_title': '私たちの優位性',
        'about_advantage_1': '✅ 最新AI技術による深度分析',
        'about_advantage_2': '✅ 多様なファイル形式のアップロード対応',
        'about_advantage_3': '✅ 求人JDと組み合わせた精密マッチング分析',
        'about_advantage_4': '✅ 詳細な最適化提案と修正プラン',
        'about_advantage_5': '✅ 高速レスポンス、即座に結果取得',
        'about_advantage_6': '✅ データセキュリティ、プライバシー保護',
        'stat_analyzed': '分析済み履歴書',
        'stat_satisfaction': 'ユーザー満足度',
        'stat_service': 'オンラインサービス',
        'footer_tagline': 'AIがあなたのキャリア発展を支援',
        'footer_contact': 'お問い合わせ: <EMAIL>',
        'footer_copyright': '© 2025 AI履歴書最適化アシスタント. All rights reserved.',
        'error_no_file': '履歴書ファイルを選択してください',
        'error_file_too_large': 'ファイルが大きすぎます。100MB未満のファイルを選択してください',
        'error_unsupported_format': 'サポートされていないファイル形式です。PDF、DOC、DOCX、JPG、PNG、TXTファイルを選択してください',
        'analyzing_message': 'AI履歴書分析を実行中です。しばらくお待ちください...',
        'analysis_complete': 'AI分析完了！（所要時間: {duration}秒）',
        'analysis_failed': '分析失敗: {error}',
        'api_error': 'API呼び出し失敗 (HTTP {status})',
        'network_error': 'ネットワークエラー（所要時間: {duration}秒）: {message}',
        'result_title': '📊 履歴書分析結果',
        'result_subtitle': 'AI技術に基づくプロフェッショナル履歴書分析レポート',
        'result_loading': '分析結果を読み込み中...',
        'result_error_title': '読み込み失敗',
        'result_error_message': '分析結果を読み込めませんでした。再試行してください。',
        'result_return_home': 'ホームページに戻って再分析',
        'result_file_name': 'ファイル名:',
        'result_analysis_time': '分析時間:',
        'result_has_jd': 'JD含む:',
        'result_print': '🖨️ レポート印刷',
        'result_new_analysis': '🔄 新規分析',
        'yes': 'はい',
        'no': 'いいえ'
    },

    // 韩语
    'ko': {
        'site_title': '🎯 AI 이력서 최적화 도우미',
        'nav_features': '기능',
        'nav_analyze': '분석 시작',
        'nav_about': '회사 소개',
        'hero_title': 'AI가 당신의 이력서를 업그레이드',
        'hero_subtitle': '전문적인 이력서 분석과 최적화 제안으로 취업에서 돋보이세요',
        'hero_ai_analysis': 'AI 스마트 분석',
        'hero_multi_format': '다중 형식 지원',
        'hero_fast_generation': '빠른 생성',
        'hero_professional_advice': '전문 조언',
        'hero_cta': '지금 분석 시작',
        'features_title': '왜 우리의 AI 이력서 분석을 선택해야 할까요',
        'feature_precise_title': '정밀 분석',
        'feature_precise_desc': '최신 AI 기술을 바탕으로 이력서 내용을 심층 분석하여 강점과 약점을 파악하고 맞춤형 개선 제안을 제공합니다',
        'feature_matching_title': '직무 매칭',
        'feature_matching_desc': '목표 직무 설명서와 결합하여 이력서와 직위의 매칭도를 분석하고 관련 기술과 경험을 강조합니다',
        'feature_optimization_title': '전문 최적화',
        'feature_optimization_desc': '구체적인 수정 제안과 최적화 방안을 제공하며, 내용 구조, 키워드 최적화, 표현 방식 등을 포함합니다',
        'feature_convenient_title': '빠르고 편리',
        'feature_convenient_desc': 'PDF, Word, 이미지 등 다양한 형식을 지원하며, 업로드만으로 상세한 분석 보고서를 얻을 수 있습니다',
        'upload_title': '이력서 분석 시작',
        'upload_subtitle': '이력서 파일을 업로드하면 AI가 전문적인 분석과 제안을 제공합니다',
        'upload_area_text': '파일 선택 또는 여기로 드래그',
        'upload_area_desc': 'PDF, DOC, DOCX, JPG, PNG 형식 지원, 최대 100MB',
        'jd_label': '💼 목표 직무 설명 (선택사항)',
        'jd_placeholder': '지원하려는 직무 설명(JD)을 붙여넣으면 AI가 더 정확한 매칭 분석을 제공합니다...',
        'submit_button': '🔍 AI 분석 시작',
        'submit_loading': '분석 중...',
        'about_title': '회사 소개',
        'about_service_title': '전문적인 AI 이력서 분석 서비스',
        'about_service_desc1': '우리는 구직자들에게 가장 전문적인 이력서 분석과 최적화 서비스를 제공하는 데 전념하고 있습니다. 첨단 AI 기술을 통해 이력서 내용을 심층 분석하고 강점과 약점을 파악하여 구체적인 개선 제안을 제공합니다.',
        'about_service_desc2': '신입사원이든 경험 많은 직장인이든, 개인 맞춤형 이력서 최적화 솔루션을 제공하여 치열한 취업 경쟁에서 돋보일 수 있도록 도와드립니다.',
        'about_advantages_title': '우리의 장점',
        'about_advantage_1': '✅ 최신 AI 기술 기반 심층 분석',
        'about_advantage_2': '✅ 다양한 파일 형식 업로드 지원',
        'about_advantage_3': '✅ 직무 JD와 결합한 정밀 매칭 분석',
        'about_advantage_4': '✅ 상세한 최적화 제안과 수정 방안',
        'about_advantage_5': '✅ 빠른 응답, 즉시 결과 획득',
        'about_advantage_6': '✅ 데이터 보안, 개인정보 보호',
        'stat_analyzed': '분석된 이력서',
        'stat_satisfaction': '사용자 만족도',
        'stat_service': '온라인 서비스',
        'footer_tagline': 'AI가 당신의 커리어 발전을 지원합니다',
        'footer_contact': '문의: <EMAIL>',
        'footer_copyright': '© 2025 AI 이력서 최적화 도우미. All rights reserved.',
        'error_no_file': '이력서 파일을 선택해주세요',
        'error_file_too_large': '파일이 너무 큽니다. 100MB 미만의 파일을 선택해주세요',
        'error_unsupported_format': '지원되지 않는 파일 형식입니다. PDF, DOC, DOCX, JPG, PNG, TXT 파일을 선택해주세요',
        'analyzing_message': 'AI 이력서 분석을 진행 중입니다. 잠시만 기다려주세요...',
        'analysis_complete': 'AI 분석 완료! (소요시간: {duration}초)',
        'analysis_failed': '분석 실패: {error}',
        'api_error': 'API 호출 실패 (HTTP {status})',
        'network_error': '네트워크 오류 (소요시간: {duration}초): {message}',
        'result_title': '📊 이력서 분석 결과',
        'result_subtitle': 'AI 기술 기반 전문 이력서 분석 보고서',
        'result_loading': '분석 결과를 불러오는 중...',
        'result_error_title': '로딩 실패',
        'result_error_message': '분석 결과를 불러올 수 없습니다. 다시 시도해주세요.',
        'result_return_home': '홈페이지로 돌아가서 재분석',
        'result_file_name': '파일명:',
        'result_analysis_time': '분석 시간:',
        'result_has_jd': 'JD 포함:',
        'result_print': '🖨️ 보고서 인쇄',
        'result_new_analysis': '🔄 새 분석',
        'yes': '예',
        'no': '아니오'
    },

    // 德语
    'de': {
        'site_title': '🎯 AI Lebenslauf-Optimierer',
        'nav_features': 'Funktionen',
        'nav_analyze': 'Analyse starten',
        'nav_about': 'Über uns',
        'hero_title': 'Lassen Sie AI Ihren Lebenslauf verbessern',
        'hero_subtitle': 'Professionelle Lebenslaufanalyse und Optimierungsvorschläge, um sich bei der Jobsuche hervorzuheben',
        'hero_ai_analysis': 'AI Smart-Analyse',
        'hero_multi_format': 'Multi-Format-Unterstützung',
        'hero_fast_generation': 'Schnelle Generierung',
        'hero_professional_advice': 'Professionelle Beratung',
        'hero_cta': 'Jetzt Analyse starten',
        'features_title': 'Warum unsere AI Lebenslauf-Analyse wählen',
        'feature_precise_title': 'Präzise Analyse',
        'feature_precise_desc': 'Basierend auf neuester AI-Technologie analysieren wir Ihren Lebenslauf tiefgreifend, identifizieren Stärken und Schwächen und bieten gezielte Verbesserungsvorschläge',
        'feature_matching_title': 'Job-Matching',
        'feature_matching_desc': 'In Kombination mit Ziel-Stellenausschreibungen analysieren wir die Übereinstimmung zwischen Lebenslauf und Position und helfen Ihnen, relevante Fähigkeiten und Erfahrungen hervorzuheben',
        'feature_optimization_title': 'Professionelle Optimierung',
        'feature_optimization_desc': 'Wir bieten spezifische Änderungsvorschläge und Optimierungspläne, einschließlich Inhaltsstruktur, Keyword-Optimierung, Ausdrucksweise usw.',
        'feature_convenient_title': 'Schnell & Bequem',
        'feature_convenient_desc': 'Unterstützt PDF, Word, Bilder und andere Formate. Laden Sie hoch und erhalten Sie detaillierte Analyseberichte',
        'upload_title': 'Beginnen Sie mit der Analyse Ihres Lebenslaufs',
        'upload_subtitle': 'Laden Sie Ihre Lebenslaufdatei hoch und unsere AI wird professionelle Analysen und Vorschläge liefern',
        'upload_area_text': 'Klicken Sie, um Datei auszuwählen oder hierher ziehen',
        'upload_area_desc': 'Unterstützt PDF, DOC, DOCX, JPG, PNG Formate, max. 100MB',
        'jd_label': '💼 Ziel-Stellenbeschreibung (Optional)',
        'jd_placeholder': 'Fügen Sie die Stellenbeschreibung (JD) ein, auf die Sie sich bewerben. Dies hilft der AI, eine genauere Matching-Analyse zu liefern...',
        'submit_button': '🔍 AI-Analyse starten',
        'submit_loading': 'Analysiere...',
        'about_title': 'Über uns',
        'about_service_title': 'Professioneller AI Lebenslauf-Analysedienst',
        'about_service_desc1': 'Wir sind bestrebt, Jobsuchenden den professionellsten Lebenslauf-Analyse- und Optimierungsservice zu bieten. Durch fortschrittliche AI-Technologie können wir Lebenslaufinhalte tiefgreifend analysieren, Stärken und Schwächen identifizieren und spezifische Verbesserungsvorschläge liefern.',
        'about_service_desc2': 'Ob Sie Absolvent oder erfahrener Berufstätiger sind, wir können personalisierte Lebenslauf-Optimierungslösungen anbieten, um Ihnen zu helfen, sich im intensiven Jobwettbewerb hervorzuheben.',
        'about_advantages_title': 'Unsere Vorteile',
        'about_advantage_1': '✅ Tiefgreifende Analyse basierend auf neuester AI-Technologie',
        'about_advantage_2': '✅ Unterstützung für mehrere Dateiformate',
        'about_advantage_3': '✅ Präzise Matching-Analyse kombiniert mit Job-JD',
        'about_advantage_4': '✅ Detaillierte Optimierungsvorschläge und Änderungspläne',
        'about_advantage_5': '✅ Schnelle Antwort, sofortige Ergebnisse',
        'about_advantage_6': '✅ Datensicherheit, Datenschutz',
        'stat_analyzed': 'Analysierte Lebensläufe',
        'stat_satisfaction': 'Benutzerzufriedenheit',
        'stat_service': 'Online-Service',
        'footer_tagline': 'Lassen Sie AI Ihre Karriereentwicklung unterstützen',
        'footer_contact': 'Kontakt: <EMAIL>',
        'footer_copyright': '© 2025 AI Lebenslauf-Optimierer. Alle Rechte vorbehalten.',
        'error_no_file': 'Bitte wählen Sie eine Lebenslaufdatei aus',
        'error_file_too_large': 'Datei zu groß, bitte wählen Sie eine Datei kleiner als 100MB',
        'error_unsupported_format': 'Nicht unterstütztes Dateiformat, bitte wählen Sie PDF, DOC, DOCX, JPG, PNG oder TXT Dateien',
        'analyzing_message': 'AI-Lebenslaufanalyse läuft, bitte warten...',
        'analysis_complete': 'AI-Analyse abgeschlossen! (Zeit: {duration}s)',
        'analysis_failed': 'Analyse fehlgeschlagen: {error}',
        'api_error': 'API-Aufruf fehlgeschlagen (HTTP {status})',
        'network_error': 'Netzwerkfehler (Zeit: {duration}s): {message}',
        'result_title': '📊 Lebenslauf-Analyseergebnisse',
        'result_subtitle': 'Professioneller Lebenslauf-Analysebericht basierend auf AI-Technologie',
        'result_loading': 'Lade Analyseergebnisse...',
        'result_error_title': 'Laden fehlgeschlagen',
        'result_error_message': 'Analyseergebnisse konnten nicht geladen werden, bitte versuchen Sie es erneut.',
        'result_return_home': 'Zur Startseite für erneute Analyse',
        'result_file_name': 'Dateiname:',
        'result_analysis_time': 'Analysezeit:',
        'result_has_jd': 'Enthält JD:',
        'result_print': '🖨️ Bericht drucken',
        'result_new_analysis': '🔄 Neue Analyse',
        'yes': 'Ja',
        'no': 'Nein'
    },

    // 俄语
    'ru': {
        'site_title': '🎯 AI Оптимизатор Резюме',
        'nav_features': 'Функции',
        'nav_analyze': 'Начать анализ',
        'nav_about': 'О нас',
        'hero_title': 'Позвольте ИИ улучшить ваше резюме',
        'hero_subtitle': 'Профессиональный анализ резюме и рекомендации по оптимизации, чтобы выделиться при поиске работы',
        'hero_ai_analysis': 'Умный анализ ИИ',
        'hero_multi_format': 'Поддержка множества форматов',
        'hero_fast_generation': 'Быстрая генерация',
        'hero_professional_advice': 'Профессиональные советы',
        'hero_cta': 'Начать анализ сейчас',
        'features_title': 'Почему выбрать наш ИИ анализ резюме',
        'feature_precise_title': 'Точный анализ',
        'feature_precise_desc': 'На основе новейших технологий ИИ глубоко анализируем содержание вашего резюме, выявляем сильные и слабые стороны, предоставляем целевые рекомендации по улучшению',
        'feature_matching_title': 'Соответствие вакансии',
        'feature_matching_desc': 'В сочетании с описанием целевой вакансии анализируем соответствие резюме и должности, помогаем выделить соответствующие навыки и опыт',
        'feature_optimization_title': 'Профессиональная оптимизация',
        'feature_optimization_desc': 'Предоставляем конкретные предложения по изменениям и планы оптимизации, включая структуру содержания, оптимизацию ключевых слов, способы выражения и т.д.',
        'feature_convenient_title': 'Быстро и удобно',
        'feature_convenient_desc': 'Поддерживает PDF, Word, изображения и другие форматы, загрузите и получите подробные аналитические отчеты',
        'upload_title': 'Начните анализ вашего резюме',
        'upload_subtitle': 'Загрузите файл резюме, и наш ИИ предоставит профессиональный анализ и предложения',
        'upload_area_text': 'Нажмите, чтобы выбрать файл или перетащите сюда',
        'upload_area_desc': 'Поддерживает форматы PDF, DOC, DOCX, JPG, PNG, максимум 100МБ',
        'jd_label': '💼 Описание целевой вакансии (необязательно)',
        'jd_placeholder': 'Вставьте описание вакансии (JD), на которую вы подаете заявку. Это поможет ИИ предоставить более точный анализ соответствия...',
        'submit_button': '🔍 Начать ИИ анализ',
        'submit_loading': 'Анализирую...',
        'about_title': 'О нас',
        'about_service_title': 'Профессиональный сервис ИИ анализа резюме',
        'about_service_desc1': 'Мы стремимся предоставить соискателям самый профессиональный сервис анализа и оптимизации резюме. Благодаря передовым технологиям ИИ мы можем глубоко анализировать содержание резюме, выявлять сильные и слабые стороны и предоставлять конкретные рекомендации по улучшению.',
        'about_service_desc2': 'Независимо от того, являетесь ли вы выпускником или опытным профессионалом, мы можем предоставить персонализированные решения по оптимизации резюме, чтобы помочь вам выделиться в жесткой конкуренции за работу.',
        'about_advantages_title': 'Наши преимущества',
        'about_advantage_1': '✅ Глубокий анализ на основе новейших технологий ИИ',
        'about_advantage_2': '✅ Поддержка загрузки множества форматов файлов',
        'about_advantage_3': '✅ Точный анализ соответствия в сочетании с JD вакансии',
        'about_advantage_4': '✅ Подробные рекомендации по оптимизации и планы изменений',
        'about_advantage_5': '✅ Быстрый ответ, мгновенные результаты',
        'about_advantage_6': '✅ Безопасность данных, защита конфиденциальности',
        'stat_analyzed': 'Проанализированных резюме',
        'stat_satisfaction': 'Удовлетворенность пользователей',
        'stat_service': 'Онлайн-сервис',
        'footer_tagline': 'Позвольте ИИ поддержать развитие вашей карьеры',
        'footer_contact': 'Связаться с нами: <EMAIL>',
        'footer_copyright': '© 2025 AI Оптимизатор Резюме. Все права защищены.',
        'error_no_file': 'Пожалуйста, выберите файл резюме',
        'error_file_too_large': 'Файл слишком большой, выберите файл меньше 100МБ',
        'error_unsupported_format': 'Неподдерживаемый формат файла, выберите файлы PDF, DOC, DOCX, JPG, PNG или TXT',
        'analyzing_message': 'Выполняется ИИ анализ резюме, пожалуйста, подождите...',
        'analysis_complete': 'ИИ анализ завершен! (Время: {duration}с)',
        'analysis_failed': 'Анализ не удался: {error}',
        'api_error': 'Ошибка вызова API (HTTP {status})',
        'network_error': 'Сетевая ошибка (Время: {duration}с): {message}',
        'result_title': '📊 Результаты анализа резюме',
        'result_subtitle': 'Профессиональный отчет анализа резюме на основе технологий ИИ',
        'result_loading': 'Загрузка результатов анализа...',
        'result_error_title': 'Ошибка загрузки',
        'result_error_message': 'Не удалось загрузить результаты анализа, попробуйте еще раз.',
        'result_return_home': 'Вернуться на главную для повторного анализа',
        'result_file_name': 'Имя файла:',
        'result_analysis_time': 'Время анализа:',
        'result_has_jd': 'Содержит JD:',
        'result_print': '🖨️ Печать отчета',
        'result_new_analysis': '🔄 Новый анализ',
        'yes': 'Да',
        'no': 'Нет'
    },

    // 法语
    'fr': {
        'site_title': '🎯 Optimiseur de CV IA',
        'nav_features': 'Fonctionnalités',
        'nav_analyze': 'Commencer l\'analyse',
        'nav_about': 'À propos',
        'hero_title': 'Laissez l\'IA améliorer votre CV',
        'hero_subtitle': 'Analyse professionnelle de CV et suggestions d\'optimisation pour vous démarquer dans votre recherche d\'emploi',
        'hero_ai_analysis': 'Analyse intelligente IA',
        'hero_multi_format': 'Support multi-format',
        'hero_fast_generation': 'Génération rapide',
        'hero_professional_advice': 'Conseils professionnels',
        'hero_cta': 'Commencer l\'analyse maintenant',
        'features_title': 'Pourquoi choisir notre analyse de CV IA',
        'feature_precise_title': 'Analyse précise',
        'feature_precise_desc': 'Basé sur la dernière technologie IA, analysez en profondeur le contenu de votre CV, identifiez les forces et faiblesses, et fournissez des suggestions d\'amélioration ciblées',
        'feature_matching_title': 'Correspondance d\'emploi',
        'feature_matching_desc': 'Combiné avec les descriptions d\'emploi cibles, analysez la correspondance entre CV et poste, aidez à mettre en évidence les compétences et expériences pertinentes',
        'feature_optimization_title': 'Optimisation professionnelle',
        'feature_optimization_desc': 'Fournir des suggestions de modification spécifiques et des plans d\'optimisation, incluant la structure du contenu, l\'optimisation des mots-clés, les méthodes d\'expression, etc.',
        'feature_convenient_title': 'Rapide et pratique',
        'feature_convenient_desc': 'Supporte PDF, Word, images et autres formats, téléchargez et obtenez des rapports d\'analyse détaillés',
        'upload_title': 'Commencez l\'analyse de votre CV',
        'upload_subtitle': 'Téléchargez votre fichier CV, notre IA fournira une analyse professionnelle et des suggestions',
        'upload_area_text': 'Cliquez pour sélectionner un fichier ou glissez ici',
        'upload_area_desc': 'Supporte les formats PDF, DOC, DOCX, JPG, PNG, max 100MB',
        'jd_label': '💼 Description d\'emploi cible (optionnel)',
        'jd_placeholder': 'Collez la description d\'emploi (JD) pour laquelle vous postulez. Cela aidera l\'IA à fournir une analyse de correspondance plus précise...',
        'submit_button': '🔍 Commencer l\'analyse IA',
        'submit_loading': 'Analyse en cours...',
        'about_title': 'À propos de nous',
        'about_service_title': 'Service professionnel d\'analyse de CV IA',
        'about_service_desc1': 'Nous nous engageons à fournir aux demandeurs d\'emploi le service d\'analyse et d\'optimisation de CV le plus professionnel. Grâce à la technologie IA avancée, nous pouvons analyser en profondeur le contenu du CV, identifier les forces et faiblesses, et fournir des suggestions d\'amélioration spécifiques.',
        'about_service_desc2': 'Que vous soyez diplômé ou professionnel expérimenté, nous pouvons fournir des solutions d\'optimisation de CV personnalisées pour vous aider à vous démarquer dans la concurrence féroce pour l\'emploi.',
        'about_advantages_title': 'Nos avantages',
        'about_advantage_1': '✅ Analyse approfondie basée sur la dernière technologie IA',
        'about_advantage_2': '✅ Support de téléchargement de multiples formats de fichiers',
        'about_advantage_3': '✅ Analyse de correspondance précise combinée avec JD d\'emploi',
        'about_advantage_4': '✅ Suggestions d\'optimisation détaillées et plans de modification',
        'about_advantage_5': '✅ Réponse rapide, résultats instantanés',
        'about_advantage_6': '✅ Sécurité des données, protection de la vie privée',
        'stat_analyzed': 'CV analysés',
        'stat_satisfaction': 'Satisfaction utilisateur',
        'stat_service': 'Service en ligne',
        'footer_tagline': 'Laissez l\'IA soutenir le développement de votre carrière',
        'footer_contact': 'Nous contacter: <EMAIL>',
        'footer_copyright': '© 2025 Optimiseur de CV IA. Tous droits réservés.',
        'error_no_file': 'Veuillez sélectionner un fichier CV',
        'error_file_too_large': 'Fichier trop volumineux, veuillez sélectionner un fichier de moins de 100MB',
        'error_unsupported_format': 'Format de fichier non supporté, veuillez sélectionner des fichiers PDF, DOC, DOCX, JPG, PNG ou TXT',
        'analyzing_message': 'Analyse IA du CV en cours, veuillez patienter...',
        'analysis_complete': 'Analyse IA terminée! (Temps: {duration}s)',
        'analysis_failed': 'Échec de l\'analyse: {error}',
        'api_error': 'Échec de l\'appel API (HTTP {status})',
        'network_error': 'Erreur réseau (Temps: {duration}s): {message}',
        'result_title': '📊 Résultats d\'analyse de CV',
        'result_subtitle': 'Rapport d\'analyse de CV professionnel basé sur la technologie IA',
        'result_loading': 'Chargement des résultats d\'analyse...',
        'result_error_title': 'Échec du chargement',
        'result_error_message': 'Impossible de charger les résultats d\'analyse, veuillez réessayer.',
        'result_return_home': 'Retourner à l\'accueil pour une nouvelle analyse',
        'result_file_name': 'Nom du fichier:',
        'result_analysis_time': 'Temps d\'analyse:',
        'result_has_jd': 'Contient JD:',
        'result_print': '🖨️ Imprimer le rapport',
        'result_new_analysis': '🔄 Nouvelle analyse',
        'yes': 'Oui',
        'no': 'Non'
    }
};

// 语言检测和设置
class LanguageManager {
    constructor() {
        this.currentLanguage = this.detectLanguage();
        this.translations = i18n[this.currentLanguage] || i18n['en'];
    }
    
    detectLanguage() {
        // 从URL参数获取语言
        const urlParams = new URLSearchParams(window.location.search);
        const urlLang = urlParams.get('lang');
        if (urlLang && i18n[urlLang]) {
            return urlLang;
        }
        
        // 从localStorage获取保存的语言
        const savedLang = localStorage.getItem('preferred_language');
        if (savedLang && i18n[savedLang]) {
            return savedLang;
        }
        
        // 从浏览器语言检测
        const browserLang = navigator.language || navigator.userLanguage;
        
        // 语言映射
        const langMap = {
            'zh-CN': 'zh-CN',
            'zh-TW': 'zh-TW',
            'zh-HK': 'zh-TW',
            'zh': 'zh-CN',
            'ja': 'ja',
            'ja-JP': 'ja',
            'ko': 'ko',
            'ko-KR': 'ko',
            'de': 'de',
            'de-DE': 'de',
            'ru': 'ru',
            'ru-RU': 'ru',
            'fr': 'fr',
            'fr-FR': 'fr',
            'en': 'en',
            'en-US': 'en',
            'en-GB': 'en'
        };
        
        const detectedLang = langMap[browserLang] || langMap[browserLang.split('-')[0]] || 'zh-CN'; // 改为中文默认

        // 如果检测到的语言有对应的翻译，使用它，否则使用中文简体
        return i18n[detectedLang] ? detectedLang : 'zh-CN';
    }
    
    t(key, params = {}) {
        let text = this.translations[key] || i18n['en'][key] || key;
        
        // 替换参数
        Object.keys(params).forEach(param => {
            text = text.replace(`{${param}}`, params[param]);
        });
        
        return text;
    }
    
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    setLanguage(lang) {
        if (i18n[lang]) {
            this.currentLanguage = lang;
            this.translations = i18n[lang];
            localStorage.setItem('preferred_language', lang);
            this.updatePageContent();
        }
    }
    
    updatePageContent() {
        // 更新页面中所有带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const text = this.t(key);
            
            if (element.tagName === 'INPUT' && element.type === 'submit') {
                element.value = text;
            } else if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                element.placeholder = text;
            } else {
                element.textContent = text;
            }
        });
        
        // 更新页面标题
        const titleElement = document.querySelector('title');
        if (titleElement) {
            titleElement.textContent = this.t('site_title');
        }
    }
}

// 全局语言管理器实例
window.langManager = new LanguageManager();
