// 主要JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化多语言
    if (window.langManager) {
        window.langManager.updatePageContent();
    }

    // 初始化
    initializeFileUpload();
    initializeFormSubmission();
    initializeSmoothScrolling();
});

// 文件上传功能初始化
function initializeFileUpload() {
    const fileInput = document.getElementById('file');
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInfo = document.getElementById('fileInfo');

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        handleFileSelection(file);
    });

    // 拖拽功能
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelection(files[0]);
        }
    });

    // 处理文件选择
    function handleFileSelection(file) {
        if (file) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            const fileType = file.type || '未知类型';
            
            fileInfo.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 1.5rem;">📄</span>
                    <div>
                        <div><strong>文件名:</strong> ${file.name}</div>
                        <div><strong>大小:</strong> ${fileSize} MB</div>
                        <div><strong>类型:</strong> ${fileType}</div>
                    </div>
                </div>
            `;
            fileInfo.style.display = 'block';
            
            // 更新上传区域显示
            fileUploadArea.innerHTML = `
                <div class="upload-icon">✅</div>
                <div class="upload-text">
                    <strong>文件已选择: ${file.name}</strong>
                    <p>点击重新选择文件</p>
                </div>
            `;
        } else {
            fileInfo.style.display = 'none';
            resetUploadArea();
        }
    }

    // 重置上传区域
    function resetUploadArea() {
        fileUploadArea.innerHTML = `
            <div class="upload-icon">📄</div>
            <div class="upload-text">
                <strong>点击选择文件或拖拽到此处</strong>
                <p>支持 PDF、DOC、DOCX、JPG、PNG 格式，最大100MB</p>
            </div>
        `;
    }
}

// 表单提交功能初始化
function initializeFormSubmission() {
    const uploadForm = document.getElementById('uploadForm');
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    const resultsSection = document.getElementById('results');
    const resultContent = document.getElementById('resultContent');

    uploadForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // 获取表单数据
        const formData = new FormData();
        const fileInput = document.getElementById('file');
        const file = fileInput.files[0];
        
        if (!file) {
            showNotification('请选择一个简历文件', 'error');
            return;
        }
        
        // 检查文件大小 (100MB限制)
        if (file.size > 100 * 1024 * 1024) {
            showNotification('文件太大，请选择小于100MB的文件', 'error');
            return;
        }
        
        // 检查文件类型
        const allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.txt'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
            showNotification('不支持的文件格式，请选择PDF、DOC、DOCX、JPG、PNG或TXT文件', 'error');
            return;
        }
        
        formData.append('file', file);
        const jdValue = document.getElementById('jd').value.trim();
        if (jdValue) {
            formData.append('jd', jdValue);
        }

        // 添加用户语言到FormData
        const userLanguage = window.langManager ? window.langManager.getCurrentLanguage() : 'en';
        formData.append('user_language', userLanguage);
        
        // 显示加载状态
        setLoadingState(true);
        const analyzingMessage = window.langManager ? window.langManager.t('analyzing_message') : '正在调用AI进行简历分析，请耐心等待...';
        showNotification(analyzingMessage, 'loading');
        
        const startTime = Date.now();
        
        try {
            // 调用API
            const response = await fetch('api.php', {
                method: 'POST',
                body: formData
            });
            
            const endTime = Date.now();
            const duration = ((endTime - startTime) / 1000).toFixed(1);
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`AI分析完成！(耗时: ${duration}秒)`, 'success');
                    displayResults(result);
                } else {
                    showNotification(`分析失败: ${result.error || '未知错误'}`, 'error');
                }
            } else {
                const errorText = await response.text();
                let errorMsg = `API调用失败 (HTTP ${response.status})`;
                
                if (response.status === 429) {
                    errorMsg = '请求频率限制，请稍后再试';
                } else if (response.status === 500) {
                    errorMsg = '服务器内部错误，请稍后重试';
                } else if (response.status === 404) {
                    errorMsg = 'API地址不存在，请检查配置';
                }
                
                showNotification(errorMsg, 'error');
                console.error('API Error:', errorText);
            }
            
        } catch (error) {
            const endTime = Date.now();
            const duration = ((endTime - startTime) / 1000).toFixed(1);
            
            let errorMsg = `网络错误 (耗时: ${duration}秒): ${error.message}`;
            
            if (error.message.includes('Failed to fetch')) {
                errorMsg += '\n\n可能的原因:\n1. 网络连接问题\n2. API服务器未响应\n3. 跨域问题';
            }
            
            showNotification(errorMsg, 'error');
            console.error('Network Error:', error);
        } finally {
            setLoadingState(false);
        }
    });

    // 设置加载状态
    function setLoadingState(loading) {
        submitBtn.disabled = loading;
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'flex';
        } else {
            btnText.style.display = 'block';
            btnLoading.style.display = 'none';
        }
    }
}

// 显示结果 - 跳转到结果页面
function displayResults(result) {
    // 将结果存储到localStorage
    localStorage.setItem('latestAnalysisResult', JSON.stringify(result));

    // 跳转到结果页面
    window.location.href = 'result.html';
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        max-width: 400px;
        font-size: 14px;
        line-height: 1.4;
        animation: slideInRight 0.3s ease-out;
    `;
    
    // 添加图标
    const icon = getNotificationIcon(type);
    notification.innerHTML = `
        <div style="display: flex; align-items: flex-start; gap: 10px;">
            <span style="font-size: 18px; flex-shrink: 0;">${icon}</span>
            <div style="flex: 1;">${message}</div>
        </div>
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, type === 'loading' ? 10000 : 5000);
}

// 获取通知颜色
function getNotificationColor(type) {
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6',
        loading: '#6366f1'
    };
    return colors[type] || colors.info;
}

// 获取通知图标
function getNotificationIcon(type) {
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️',
        loading: '🔄'
    };
    return icons[type] || icons.info;
}

// 平滑滚动功能
function initializeSmoothScrolling() {
    // 为所有内部链接添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
