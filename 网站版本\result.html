<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analysis Results - AI Resume Optimizer</title>
    <meta name="description" content="View your resume analysis results and optimization suggestions">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/marked.min.js"></script>
    <script src="js/i18n.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1 data-i18n="site_title">🎯 AI Resume Optimizer</h1>
                </div>
                <nav class="nav">
                    <a href="index.html">Return to Homepage</a>
                    <a href="index.html#features" data-i18n="nav_features">Features</a>
                    <a href="index.html#about" data-i18n="nav_about">About Us</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Results Section -->
    <section class="results-page">
        <div class="container">
            <div class="results-header">
                <h1 class="page-title" data-i18n="result_title">📊 Resume Analysis Results</h1>
                <p class="page-subtitle" data-i18n="result_subtitle">Professional resume analysis report based on AI technology</p>
            </div>
            
            <!-- Loading State -->
            <div id="loadingState" class="loading-container">
                <div class="loading-spinner"></div>
                <p data-i18n="result_loading">Loading analysis results...</p>
            </div>

            <!-- Error State -->
            <div id="errorState" class="error-container" style="display: none;">
                <div class="error-icon">❌</div>
                <h3 data-i18n="result_error_title">Loading Failed</h3>
                <p id="errorMessage" data-i18n="result_error_message">Unable to load analysis results, please try again.</p>
                <a href="index.html" class="retry-button" data-i18n="result_return_home">Return to Homepage for Re-analysis</a>
            </div>
            
            <!-- Results Content -->
            <div id="resultContent" class="result-content" style="display: none;">
                <div class="result-meta">
                    <div class="meta-item">
                        <span class="meta-label" data-i18n="result_file_name">File Name:</span>
                        <span id="fileName">-</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label" data-i18n="result_analysis_time">Analysis Time:</span>
                        <span id="analysisTime">-</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label" data-i18n="result_has_jd">Includes JD:</span>
                        <span id="hasJD">-</span>
                    </div>
                </div>
                
                <div class="result-body" id="resultBody">
                    <!-- AI分析结果将在这里显示 -->
                </div>
                
                <div class="result-actions">
                    <button onclick="window.print()" class="action-button print-button" data-i18n="result_print">
                        🖨️ Print Report
                    </button>
                    <a href="index.html" class="action-button new-analysis-button" data-i18n="result_new_analysis">
                        🔄 New Analysis
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <h3 data-i18n="site_title">🎯 AI Resume Optimizer</h3>
                    <p data-i18n="footer_tagline">Let AI empower your career development</p>
                </div>
                <div class="footer-links">
                    <a href="index.html#features" data-i18n="nav_features">Features</a>
                    <a href="index.html#upload" data-i18n="nav_analyze">Start Analysis</a>
                    <a href="index.html#about" data-i18n="nav_about">About Us</a>
                </div>
                <div class="footer-contact">
                    <p data-i18n="footer_contact">Contact us: <EMAIL></p>
                    <p>Website: https://www.furrywoo.com</p>
                    <p data-i18n="footer_copyright">&copy; 2025 AI Resume Optimizer. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 页面加载时获取分析结果
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化多语言
            if (window.langManager) {
                window.langManager.updatePageContent();
            }

            loadAnalysisResult();
        });

        function loadAnalysisResult() {
            // 从URL参数或localStorage获取结果数据
            const urlParams = new URLSearchParams(window.location.search);
            const resultId = urlParams.get('id');
            
            // 尝试从localStorage获取最新结果
            const storedResult = localStorage.getItem('latestAnalysisResult');
            
            if (storedResult) {
                try {
                    const result = JSON.parse(storedResult);
                    displayResult(result);
                } catch (e) {
                    showError('数据解析失败');
                }
            } else if (resultId) {
                // 如果有ID，可以从服务器获取结果（需要实现相应的API）
                showError('暂不支持通过ID获取历史结果');
            } else {
                showError('未找到分析结果数据');
            }
        }

        function displayResult(result) {
            const loadingState = document.getElementById('loadingState');
            const resultContent = document.getElementById('resultContent');
            const resultBody = document.getElementById('resultBody');
            
            // 隐藏加载状态
            loadingState.style.display = 'none';
            
            // 显示结果内容
            resultContent.style.display = 'block';
            
            // 填充元数据
            document.getElementById('fileName').textContent = result.file_info?.name || 'Unknown File';
            document.getElementById('analysisTime').textContent = new Date().toLocaleString();
            const hasJDText = result.file_info?.has_jd ?
                (window.langManager ? window.langManager.t('yes') : 'Yes') :
                (window.langManager ? window.langManager.t('no') : 'No');
            document.getElementById('hasJD').textContent = hasJDText;
            
            // 渲染分析结果
            if (typeof marked !== 'undefined' && result.formatted_text) {
                resultBody.innerHTML = marked.parse(result.formatted_text);
            } else {
                const noResultText = window.langManager ? window.langManager.t('no_analysis_result') : 'No analysis results available';
                resultBody.innerHTML = `<pre style="white-space: pre-wrap; font-family: inherit;">${escapeHtml(result.formatted_text || result.raw_response || noResultText)}</pre>`;
            }
            
            // 清理localStorage
            localStorage.removeItem('latestAnalysisResult');
        }

        function showError(message) {
            const loadingState = document.getElementById('loadingState');
            const errorState = document.getElementById('errorState');
            const errorMessage = document.getElementById('errorMessage');
            
            loadingState.style.display = 'none';
            errorState.style.display = 'block';
            errorMessage.textContent = message;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
