<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API功能测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="file">选择测试文件:</label>
                <input type="file" id="file" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt" required>
            </div>
            
            <div class="form-group">
                <label for="jd">JD描述 (可选):</label>
                <textarea id="jd" name="jd" placeholder="输入岗位描述进行测试..."></textarea>
            </div>
            
            <button type="submit" id="submitBtn">🚀 测试API</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const result = document.getElementById('result');
            
            // 获取表单数据
            const formData = new FormData();
            const fileInput = document.getElementById('file');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('请选择一个文件', 'error');
                return;
            }
            
            formData.append('file', file);
            const jdValue = document.getElementById('jd').value.trim();
            if (jdValue) {
                formData.append('jd', jdValue);
            }
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '测试中...';
            showResult('正在测试API连接和功能...', 'loading');
            
            const startTime = Date.now();
            
            try {
                // 调用API
                const response = await fetch('api.php', {
                    method: 'POST',
                    body: formData
                });
                
                const endTime = Date.now();
                const duration = ((endTime - startTime) / 1000).toFixed(1);
                
                const responseText = await response.text();
                
                if (response.ok) {
                    try {
                        const jsonResult = JSON.parse(responseText);
                        
                        let displayResult = `✅ API测试成功！(耗时: ${duration}秒)\n\n`;
                        displayResult += `📊 响应数据:\n`;
                        displayResult += `- 成功状态: ${jsonResult.success}\n`;
                        displayResult += `- 文件名: ${jsonResult.file_info?.name || '未知'}\n`;
                        displayResult += `- 文件类型: ${jsonResult.file_info?.type || '未知'}\n`;
                        displayResult += `- 包含JD: ${jsonResult.file_info?.has_jd ? '是' : '否'}\n`;
                        displayResult += `- 处理时间: ${jsonResult.processing_time || 'N/A'}ms\n`;
                        displayResult += `- 保存ID: ${jsonResult.saved_id || 'N/A'}\n\n`;
                        
                        if (jsonResult.raw_response) {
                            displayResult += `📝 AI响应预览 (前500字符):\n`;
                            displayResult += jsonResult.raw_response.substring(0, 500);
                            if (jsonResult.raw_response.length > 500) {
                                displayResult += '\n...(内容已截断)';
                            }
                        }
                        
                        showResult(displayResult, 'success');
                    } catch (e) {
                        showResult(`✅ API响应成功，但JSON解析失败 (耗时: ${duration}秒)\n\n原始响应:\n${responseText}`, 'success');
                    }
                } else {
                    let errorMsg = `❌ API测试失败 (HTTP ${response.status}, 耗时: ${duration}秒)\n\n`;
                    errorMsg += `响应内容:\n${responseText}`;
                    showResult(errorMsg, 'error');
                }
                
            } catch (error) {
                const endTime = Date.now();
                const duration = ((endTime - startTime) / 1000).toFixed(1);
                
                let errorMsg = `❌ 网络错误 (耗时: ${duration}秒)\n\n`;
                errorMsg += `错误信息: ${error.message}\n\n`;
                errorMsg += `可能的原因:\n`;
                errorMsg += `1. API文件不存在或路径错误\n`;
                errorMsg += `2. 服务器配置问题\n`;
                errorMsg += `3. 网络连接问题\n`;
                errorMsg += `4. CORS跨域问题`;
                
                showResult(errorMsg, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 测试API';
            }
        });
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = 'result ' + type;
        }
    </script>
</body>
</html>
