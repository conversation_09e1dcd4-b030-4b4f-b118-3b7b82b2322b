<PERSON>
Software Engineer

Contact Information:
Email: <EMAIL>
Phone: ******-0123
Location: San Francisco, CA

Professional Summary:
Experienced software engineer with 5 years of expertise in web development, specializing in JavaScript, Python, and cloud technologies. Proven track record of delivering scalable applications and leading cross-functional teams.

Work Experience:

Senior Software Engineer | Tech Corp | 2022 - Present
• Developed and maintained microservices architecture using Node.js and Docker
• Led a team of 4 developers in building customer-facing web applications
• Implemented CI/CD pipelines reducing deployment time by 60%
• Collaborated with product managers to define technical requirements

Software Engineer | StartupXYZ | 2020 - 2022
• Built responsive web applications using React and Redux
• Optimized database queries improving application performance by 40%
• Participated in code reviews and mentored junior developers
• Integrated third-party APIs for payment processing and analytics

Junior Developer | WebSolutions Inc | 2019 - 2020
• Developed frontend components using HTML, CSS, and JavaScript
• Assisted in backend development using Python and Django
• Participated in agile development processes and daily standups
• Fixed bugs and implemented feature requests

Education:
Bachelor of Science in Computer Science
University of California, Berkeley | 2015 - 2019
GPA: 3.7/4.0

Technical Skills:
• Programming Languages: JavaScript, Python, Java, TypeScript
• Frontend: React, Vue.js, HTML5, CSS3, Bootstrap
• Backend: Node.js, Django, Express.js, RESTful APIs
• Databases: PostgreSQL, MongoDB, Redis
• Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins
• Tools: Git, JIRA, Slack, VS Code

Projects:
E-commerce Platform (2023)
• Built a full-stack e-commerce application using React and Node.js
• Implemented secure payment processing and user authentication
• Deployed on AWS with auto-scaling capabilities

Task Management App (2022)
• Developed a collaborative task management tool using Vue.js
• Integrated real-time notifications using WebSocket
• Achieved 99.9% uptime with proper error handling

Certifications:
• AWS Certified Solutions Architect (2023)
• Google Cloud Professional Developer (2022)

Languages:
• English (Native)
• Spanish (Conversational)
