<PERSON>-Entwickler

Kontaktinformationen:
E-Mail: <EMAIL>
Telefon: +49-30-12345678
Adresse: Berlin, Deutschland

Berufserfahrung:

Senior Software-Entwickler | TechCorp GmbH | 2022 - Heute
• Entwicklung und Wartung von Microservices-Architektur mit Node.js und Docker
• Führung eines 4-köpfigen Entwicklerteams beim Aufbau kundenorientierter Webanwendungen
• Implementierung von CI/CD-Pipelines mit 60% Reduzierung der Deployment-Zeit
• Zusammenarbeit mit Produktmanagern zur Definition technischer Anforderungen

Software-Entwickler | StartupXYZ | 2020 - 2022
• Entwicklung responsiver Webanwendungen mit React und Redux
• Optimierung von Datenbankabfragen mit 40% Leistungsverbesserung
• Teilnahme an Code-Reviews und Mentoring von Junior-Entwicklern
• Integration von Drittanbieter-APIs für Zahlungsabwicklung und Analytics

Junior-Entwickler | WebLösungen AG | 2019 - 2020
• Entwicklung von Frontend-Komponenten mit HTML, CSS und JavaScript
• Unterstützung bei Backend-Entwicklung mit Python und Django
• Teilnahme an agilen Entwicklungsprozessen und täglichen Standups
• Fehlerbehebung und Implementierung von Feature-Requests

Ausbildung:
Bachelor of Science in Informatik
Technische Universität Berlin | 2015 - 2019
Note: 1,7

Technische Fähigkeiten:
• Programmiersprachen: JavaScript, Python, Java, TypeScript
• Frontend: React, Vue.js, HTML5, CSS3, Bootstrap
• Backend: Node.js, Django, Express.js, RESTful APIs
• Datenbanken: PostgreSQL, MongoDB, Redis
• Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins
• Tools: Git, JIRA, Slack, VS Code

Projekte:
E-Commerce-Plattform (2023)
• Entwicklung einer Full-Stack E-Commerce-Anwendung mit React und Node.js
• Implementierung sicherer Zahlungsabwicklung und Benutzerauthentifizierung
• Deployment auf AWS mit Auto-Scaling-Funktionen

Task-Management-App (2022)
• Entwicklung eines kollaborativen Task-Management-Tools mit Vue.js
• Integration von Echtzeit-Benachrichtigungen mit WebSocket
• Erreichen von 99,9% Uptime durch ordnungsgemäße Fehlerbehandlung

Zertifizierungen:
• AWS Certified Solutions Architect (2023)
• Google Cloud Professional Developer (2022)

Sprachen:
• Deutsch (Muttersprache)
• Englisch (Fließend)
