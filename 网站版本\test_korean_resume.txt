김철수
소프트웨어 엔지니어

연락처 정보:
이메일: <EMAIL>
전화: 010-1234-5678
주소: 서울특별시 강남구

경력:

시니어 소프트웨어 엔지니어 | 테크코퍼레이션 | 2022년 - 현재
• Node.js와 Docker를 사용하여 마이크로서비스 아키텍처 개발 및 유지보수
• 4명의 개발자 팀을 이끌어 고객 대상 웹 애플리케이션 구축
• CI/CD 파이프라인 구현으로 배포 시간 60% 단축
• 제품 관리자와 협력하여 기술 요구사항 정의

소프트웨어 엔지니어 | 스타트업XYZ | 2020년 - 2022년
• React와 Redux를 사용하여 반응형 웹 애플리케이션 구축
• 데이터베이스 쿼리 최적화로 애플리케이션 성능 40% 향상
• 코드 리뷰 참여 및 주니어 개발자 멘토링
• 결제 처리 및 분석을 위한 서드파티 API 통합

주니어 개발자 | 웹솔루션즈 주식회사 | 2019년 - 2020년
• HTML, CSS, JavaScript를 사용하여 프론트엔드 컴포넌트 개발
• Python과 Django를 사용한 백엔드 개발 지원
• 애자일 개발 프로세스 및 일일 스탠드업 참여
• 버그 수정 및 기능 요청 구현

학력:
컴퓨터 과학 학사
서울대학교 | 2015년 - 2019년
GPA: 3.7/4.0

기술 스킬:
• 프로그래밍 언어: JavaScript, Python, Java, TypeScript
• 프론트엔드: React, Vue.js, HTML5, CSS3, Bootstrap
• 백엔드: Node.js, Django, Express.js, RESTful APIs
• 데이터베이스: PostgreSQL, MongoDB, Redis
• 클라우드 & DevOps: AWS, Docker, Kubernetes, Jenkins
• 도구: Git, JIRA, Slack, VS Code

프로젝트:
전자상거래 플랫폼 (2023년)
• React와 Node.js를 사용하여 풀스택 전자상거래 애플리케이션 구축
• 안전한 결제 처리 및 사용자 인증 구현
• 자동 확장 기능을 갖춘 AWS에 배포

작업 관리 앱 (2022년)
• Vue.js를 사용하여 협업 작업 관리 도구 개발
• WebSocket을 사용하여 실시간 알림 통합
• 적절한 오류 처리로 99.9% 가동 시간 달성

자격증:
• AWS 공인 솔루션 아키텍트 (2023년)
• Google Cloud Professional Developer (2022년)

언어:
• 한국어 (모국어)
• 영어 (비즈니스 수준)
