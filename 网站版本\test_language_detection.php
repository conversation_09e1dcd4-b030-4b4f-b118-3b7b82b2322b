<?php
/**
 * 语言检测测试页面
 */

// 包含API文件中的函数
require_once 'api.php';

// 测试用例 - 8种语言支持
$test_cases = [
    [
        'name' => '英文简历',
        'content' => '<PERSON> Software Engineer Experience: 5 years in web development Skills: JavaScript, PHP, Python Education: Bachelor of Computer Science University work project management',
        'expected' => 'en'
    ],
    [
        'name' => '中文简体简历',
        'content' => '张三 软件工程师 工作经验：5年网站开发经验 技能：JavaScript、PHP、Python 教育背景：计算机科学学士 公司项目管理经验',
        'expected' => 'zh-CN'
    ],
    [
        'name' => '中文繁体简历',
        'content' => '張三 軟體工程師 工作經驗：5年網站開發經驗 技術：JavaScript、PHP、Python 教育背景：電腦科學學士 公司項目管理經驗',
        'expected' => 'zh-TW'
    ],
    [
        'name' => '日语简历',
        'content' => '田中太郎 ソフトウェアエンジニア 職歴：ウェブ開発5年の経験 技能：JavaScript、PHP、Python 学歴：コンピュータサイエンス学士 会社でのプロジェクト管理経験',
        'expected' => 'ja'
    ],
    [
        'name' => '韩语简历',
        'content' => '김철수 소프트웨어 엔지니어 경력：웹 개발 5년 경험 기술：JavaScript, PHP, Python 학력：컴퓨터 과학 학사 회사 프로젝트 관리 경험',
        'expected' => 'ko'
    ],
    [
        'name' => '德语简历',
        'content' => 'Hans Müller Software-Entwickler Berufserfahrung: 5 Jahre Webentwicklung Fähigkeiten: JavaScript, PHP, Python Ausbildung: Bachelor in Informatik Unternehmen Projektmanagement',
        'expected' => 'de'
    ],
    [
        'name' => '俄语简历',
        'content' => 'Иван Петров Программист Опыт работы: 5 лет веб-разработки Навыки: JavaScript, PHP, Python Образование: Бакалавр компьютерных наук Управление проектами в компании',
        'expected' => 'ru'
    ],
    [
        'name' => '法语简历',
        'content' => 'Pierre Dupont Ingénieur logiciel Expérience: 5 ans de développement web Compétences: JavaScript, PHP, Python Formation: Licence en informatique Gestion de projet en entreprise',
        'expected' => 'fr'
    ],
    [
        'name' => '混合内容（主要英文）',
        'content' => 'John Smith 软件工程师 Experience: 5 years in web development Skills: JavaScript, PHP, Python University education project management work',
        'expected' => 'en'
    ],
    [
        'name' => '混合内容（主要中文）',
        'content' => '张三 Software Engineer 工作经验：5年网站开发经验 技能：JavaScript、PHP、Python 教育背景：计算机科学学士 公司项目管理',
        'expected' => 'zh-CN'
    ]
];

echo "<h1>语言检测测试结果</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-case { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .pass { background-color: #d4edda; border-color: #c3e6cb; }
    .fail { background-color: #f8d7da; border-color: #f5c6cb; }
    .content { background-color: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; }
</style>";

foreach ($test_cases as $test) {
    $detected = detectResumeLanguage($test['content']);
    $passed = $detected === $test['expected'];
    
    echo "<div class='test-case " . ($passed ? 'pass' : 'fail') . "'>";
    echo "<h3>" . $test['name'] . " - " . ($passed ? '✅ 通过' : '❌ 失败') . "</h3>";
    echo "<div class='content'><strong>测试内容：</strong><br>" . htmlspecialchars($test['content']) . "</div>";
    echo "<p><strong>期望结果：</strong> " . $test['expected'] . "</p>";
    echo "<p><strong>检测结果：</strong> " . $detected . "</p>";
    echo "</div>";
}

echo "<h2>实时测试</h2>";
echo "<form method='post'>";
echo "<textarea name='test_content' rows='5' cols='80' placeholder='输入要测试的简历内容...'>" . ($_POST['test_content'] ?? '') . "</textarea><br><br>";
echo "<input type='submit' value='检测语言'>";
echo "</form>";

if (isset($_POST['test_content']) && !empty($_POST['test_content'])) {
    $test_content = $_POST['test_content'];
    $detected_lang = detectResumeLanguage($test_content);
    echo "<div class='test-case'>";
    echo "<h3>实时检测结果</h3>";
    echo "<div class='content'><strong>输入内容：</strong><br>" . htmlspecialchars($test_content) . "</div>";
    echo "<p><strong>检测到的语言：</strong> " . $detected_lang . "</p>";
    echo "</div>";
}
?>
