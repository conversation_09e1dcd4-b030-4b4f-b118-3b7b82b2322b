/**
 * 多语言自动化更新脚本
 * 使用方法：
 * 1. 在浏览器中打开需要更新的页面
 * 2. 打开开发者工具控制台 (F12)
 * 3. 复制粘贴此脚本并回车执行
 * 4. 脚本会自动检测并添加缺失的 data-i18n 属性
 */

// 翻译映射表 - 中文到英文的对应关系
const translationMap = {
    // 通用
    '🎯 AI简历优化助手': { key: 'site_title', en: '🎯 AI Resume Optimizer' },
    '让AI为您的简历加分': { key: 'hero_title', en: 'Let AI Enhance Your Resume' },
    '专业的简历分析与优化建议，助您在求职路上脱颖而出': { key: 'hero_subtitle', en: 'Professional resume analysis and optimization suggestions to help you stand out in your job search' },

    // 导航
    '功能特色': { key: 'nav_features', en: 'Features' },
    '开始分析': { key: 'nav_analyze', en: 'Start Analysis' },
    '关于我们': { key: 'nav_about', en: 'About Us' },

    // Hero区域
    'AI智能分析': { key: 'hero_ai_analysis', en: 'AI Smart Analysis' },
    '多格式支持': { key: 'hero_multi_format', en: 'Multi-format Support' },
    '快速生成': { key: 'hero_fast_generation', en: 'Fast Generation' },
    '专业建议': { key: 'hero_professional_advice', en: 'Professional Advice' },
    '立即开始分析': { key: 'hero_cta', en: 'Start Analysis Now' },

    // Features
    '为什么选择我们的AI简历分析': { key: 'features_title', en: 'Why Choose Our AI Resume Analysis' },
    '精准分析': { key: 'feature_precise_title', en: 'Precise Analysis' },
    '岗位匹配': { key: 'feature_matching_title', en: 'Job Matching' },
    '专业优化': { key: 'feature_optimization_title', en: 'Professional Optimization' },
    '快速便捷': { key: 'feature_convenient_title', en: 'Fast & Convenient' },

    // Upload
    '开始分析您的简历': { key: 'upload_title', en: 'Start Analyzing Your Resume' },
    '上传您的简历文件，我们的AI将为您提供专业的分析和建议': { key: 'upload_subtitle', en: 'Upload your resume file, our AI will provide professional analysis and suggestions' },
    '点击选择文件或拖拽到此处': { key: 'upload_area_text', en: 'Click to select file or drag here' },
    '支持 PDF、DOC、DOCX、JPG、PNG 格式，最大100MB': { key: 'upload_area_desc', en: 'Support PDF, DOC, DOCX, JPG, PNG formats, max 100MB' },
    '💼 目标岗位描述 (可选)': { key: 'jd_label', en: '💼 Target Job Description (Optional)' },
    '🔍 开始AI分析': { key: 'submit_button', en: '🔍 Start AI Analysis' },
    '分析中...': { key: 'submit_loading', en: 'Analyzing...' },

    // About
    '专业的AI简历分析服务': { key: 'about_service_title', en: 'Professional AI Resume Analysis Service' },
    '我们的优势': { key: 'about_advantages_title', en: 'Our Advantages' },
    '简历已分析': { key: 'stat_analyzed', en: 'Resumes Analyzed' },
    '用户满意度': { key: 'stat_satisfaction', en: 'User Satisfaction' },
    '在线服务': { key: 'stat_service', en: 'Online Service' },

    // Footer
    '让AI为您的职业发展助力': { key: 'footer_tagline', en: 'Let AI empower your career development' },

    // Result Page
    '📊 简历分析结果': { key: 'result_title', en: '📊 Resume Analysis Results' },
    '基于AI技术的专业简历分析报告': { key: 'result_subtitle', en: 'Professional resume analysis report based on AI technology' },
    '正在加载分析结果...': { key: 'result_loading', en: 'Loading analysis results...' },
    '加载失败': { key: 'result_error_title', en: 'Loading Failed' },
    '无法加载分析结果，请重试。': { key: 'result_error_message', en: 'Unable to load analysis results, please try again.' },
    '返回首页重新分析': { key: 'result_return_home', en: 'Return to Homepage for Re-analysis' },
    '文件名:': { key: 'result_file_name', en: 'File Name:' },
    '分析时间:': { key: 'result_analysis_time', en: 'Analysis Time:' },
    '包含JD:': { key: 'result_has_jd', en: 'Includes JD:' },
    '🖨️ 打印报告': { key: 'result_print', en: '🖨️ Print Report' },
    '🔄 重新分析': { key: 'result_new_analysis', en: '🔄 New Analysis' }
};

// 主要更新函数
function updateHTMLForI18n() {
    let updateCount = 0;

    console.log('🚀 开始自动更新多语言属性...');

    // 遍历所有文本节点，查找需要翻译的内容
    function processElement(element) {
        // 跳过已经有 data-i18n 属性的元素
        if (element.hasAttribute && element.hasAttribute('data-i18n')) {
            return;
        }

        // 处理文本内容
        if (element.nodeType === Node.TEXT_NODE) {
            const text = element.textContent.trim();
            if (text && translationMap[text]) {
                const parent = element.parentElement;
                if (parent && !parent.hasAttribute('data-i18n')) {
                    parent.setAttribute('data-i18n', translationMap[text].key);
                    parent.textContent = translationMap[text].en;
                    updateCount++;
                    console.log(`✅ 更新: "${text}" -> "${translationMap[text].en}"`);
                }
            }
        } else if (element.nodeType === Node.ELEMENT_NODE) {
            // 处理元素的直接文本内容
            const directText = Array.from(element.childNodes)
                .filter(node => node.nodeType === Node.TEXT_NODE)
                .map(node => node.textContent.trim())
                .join(' ')
                .trim();

            if (directText && translationMap[directText]) {
                element.setAttribute('data-i18n', translationMap[directText].key);
                element.textContent = translationMap[directText].en;
                updateCount++;
                console.log(`✅ 更新元素: "${directText}" -> "${translationMap[directText].en}"`);
                return; // 不再处理子元素
            }

            // 处理特殊属性
            if (element.placeholder && translationMap[element.placeholder]) {
                element.setAttribute('data-i18n', translationMap[element.placeholder].key);
                element.placeholder = translationMap[element.placeholder].en;
                updateCount++;
                console.log(`✅ 更新placeholder: "${element.placeholder}"`);
            }

            // 递归处理子元素
            Array.from(element.childNodes).forEach(processElement);
        }
    }

    // 开始处理整个文档
    processElement(document.body);

    // 特殊处理一些复杂的元素
    updateSpecialElements();

    console.log(`🎉 更新完成！共更新了 ${updateCount} 个元素`);
    console.log('💡 提示：刷新页面查看效果，或手动调用 window.langManager.updatePageContent() 立即应用翻译');

    return updateCount;
}

// 处理特殊元素
function updateSpecialElements() {
    // 处理复杂的textarea placeholder
    const jdTextarea = document.getElementById('jd');
    if (jdTextarea && jdTextarea.placeholder.includes('请粘贴')) {
        jdTextarea.setAttribute('data-i18n', 'jd_placeholder');
        jdTextarea.placeholder = `Please paste the job description (JD) you are applying for, this will help AI provide more accurate matching analysis...

Example:
Position: Senior Frontend Engineer
Requirements:
- 3+ years frontend development experience
- Proficient in React, Vue frameworks
- Good coding standards and teamwork skills
- Mobile development experience preferred`;
    }

    // 处理按钮内的span元素
    const submitBtn = document.querySelector('.btn-text');
    if (submitBtn && submitBtn.textContent.includes('开始AI分析')) {
        submitBtn.setAttribute('data-i18n', 'submit_button');
        submitBtn.textContent = '🔍 Start AI Analysis';
    }

    const loadingText = document.querySelector('.btn-loading span:not(.spinner)');
    if (loadingText && loadingText.textContent.includes('分析中')) {
        loadingText.setAttribute('data-i18n', 'submit_loading');
        loadingText.textContent = 'Analyzing...';
    }
}

// 验证更新结果
function validateI18nUpdates() {
    const elementsWithI18n = document.querySelectorAll('[data-i18n]');
    console.log(`📊 统计：页面共有 ${elementsWithI18n.length} 个多语言元素`);

    // 检查是否有遗漏的中文内容
    const chineseRegex = /[\u4e00-\u9fff]+/;
    const allElements = document.querySelectorAll('*');
    const missedElements = [];

    allElements.forEach(element => {
        if (element.textContent && chineseRegex.test(element.textContent) &&
            !element.hasAttribute('data-i18n') &&
            element.children.length === 0) {
            missedElements.push({
                element: element,
                text: element.textContent.trim()
            });
        }
    });

    if (missedElements.length > 0) {
        console.warn('⚠️ 发现可能遗漏的中文内容：');
        missedElements.forEach(item => {
            console.warn(`- "${item.text}" (${item.element.tagName})`);
        });
    } else {
        console.log('✅ 所有中文内容都已处理完成！');
    }

    return missedElements;
}

// 导出函数供外部使用
window.i18nUpdater = {
    update: updateHTMLForI18n,
    validate: validateI18nUpdates,
    translationMap: translationMap
};

// 如果在浏览器中运行，提供使用说明
if (typeof window !== 'undefined') {
    console.log(`
🌍 多语言更新工具已加载！

使用方法：
1. 运行更新：i18nUpdater.update()
2. 验证结果：i18nUpdater.validate()
3. 立即应用翻译：window.langManager?.updatePageContent()

或者直接运行：updateHTMLForI18n()
    `);
}
