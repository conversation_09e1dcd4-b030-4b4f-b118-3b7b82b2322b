# AI简历审查助手 - 部署说明

## 项目概述
本项目将简历警察V3的AI简历审查功能整合到现有的微信小程序中，使用中转API调用Gemini进行简历分析。

## 已完成的开发工作

### 1. 后端API开发
- ✅ 创建了新的中转API文件 `jianli.php`
- ✅ 集成了简历警察V3的完整Prompt逻辑
- ✅ 支持文档内容提取（PDF、DOC、DOCX、图片）
- ✅ 添加了JD岗位描述参数支持
- ✅ 实现了五步审查流程的结构化输出
- ✅ 修改了 `api/config.php` 配置文件
- ✅ 适配了 `api/upload.php` 上传处理

### 2. 前端小程序改造
- ✅ 修改了 `app.json`，更新小程序名称为"AI简历审查助手"
- ✅ 重构了 `pages/upload/upload` 页面
  - 添加了JD描述输入框
  - 优化了文件选择界面提示
  - 调整了高级选项以适配简历场景
- ✅ 改造了 `pages/result/result` 页面
  - 适配了五步审查结果展示
  - 实现了简历分析结果的模块化显示
- ✅ 更新了相关JS逻辑以支持JD参数传递

### 3. 数据库结构调整
- ✅ 更新了数据库结构 `create_tables.sql`
- ✅ 修改表名和字段以适配简历场景
- ✅ 添加了JD描述存储字段
- ✅ 创建了简历分析结果存储方案

## 部署步骤

### 1. 服务器环境准备
确保服务器具备以下环境：
- PHP 7.4+
- MySQL 5.7+
- cURL扩展
- 支持文件上传（最大100MB）

### 2. 数据库配置
1. 创建数据库：
```sql
-- 执行 create_tables.sql 文件
mysql -u root -p < create_tables.sql
```

2. 更新数据库连接信息：
```php
// 在 api/config.php 中
define('DB_HOST', '127.0.0.1');
define('DB_NAME', 'jianli');
define('DB_USER', 'root');
define('DB_PASS', 'jianli_review');
```

### 3. API部署
1. 将 `jianli.php` 上传到服务器的 `https://www.furrywoo.com/jianli/` 目录
2. 确保API密钥配置正确：
```php
$apiKey = 'AIzaSyCkt3alFLjYSjG1nkYeM3UuXHktr3NMPKQ'; // 替换为实际密钥
```

### 4. 后端API部署
1. 将 `简历辅助审查助手/api/` 目录上传到 `https://jianli.alidog.cn/api/`
2. 确保文件权限正确，支持文件上传

### 5. 小程序部署
1. 使用微信开发者工具打开 `简历辅助审查助手/小程序/` 目录
2. 配置小程序AppID和相关信息
3. 上传代码并提交审核

## 配置说明

### API配置
- **中转API地址**: https://www.furrywoo.com/jianli/jianli.php
- **项目域名**: https://jianli.alidog.cn
- **数据库**: 本地MySQL，数据库名 `jianli`

### 功能特性
- 支持PDF、DOC、DOCX、图片格式的简历文件
- 可选的JD岗位描述输入，提高分析精准度
- 五步审查流程：第一印象、深度审计、修改蓝图、修改范本、行动清单
- 移动端友好的结果展示界面

## 测试验证

### 基本功能测试
1. 上传简历文件（PDF/DOC/图片）
2. 可选输入JD岗位描述
3. 查看分析结果的五个模块
4. 复制分析结果文本

### 预期结果
- 简历上传成功
- AI分析返回结构化结果
- 前端正确显示分析内容
- 用户可以查看和复制结果

## 注意事项

1. **API密钥安全**: 确保Gemini API密钥的安全性
2. **文件大小限制**: 单个文件最大100MB
3. **响应时间**: AI分析可能需要30秒-2分钟
4. **错误处理**: 已添加完整的错误处理和日志记录

## 后续优化建议

1. 添加PDF文档解析库以支持文本提取
2. 实现用户登录和历史记录功能
3. 添加简历评分可视化图表
4. 优化移动端UI/UX体验
5. 添加批量简历分析功能

## 技术栈
- **前端**: 微信小程序
- **后端**: PHP + MySQL
- **AI服务**: Google Gemini API
- **文件处理**: cURL + 多模态API调用
