# 简历审查助手 - 问题排查指南

## 当前问题分析

### 问题1: API返回429错误
**错误信息**: `API返回错误状态码: 429`
**原因**: 请求频率限制（Rate Limiting）
**解决方案**:
1. 检查Gemini API的配额和限制
2. 添加请求间隔控制
3. 考虑使用不同的API密钥或升级配额

### 问题2: 中转API未收到请求
**现象**: 中转API服务器没有日志记录
**可能原因**:
1. 中转API文件未正确部署到服务器
2. API地址配置错误
3. 服务器权限问题

## 排查步骤

### 第一步: 验证中转API部署
1. 确认 `jianli.php` 已上传到 `https://www.furrywoo.com/jianli/` 目录
2. 直接访问 `https://www.furrywoo.com/jianli/jianli.php` 检查是否返回405错误（正常）
3. 使用提供的测试页面 `jianli_test.html` 进行测试

### 第二步: 检查API配置
1. 验证Gemini API密钥是否有效
2. 检查API配额是否充足
3. 确认模型名称是否正确

### 第三步: 测试本地API
1. 使用 `简历辅助审查助手/api/test.html` 测试本地API
2. 检查本地API日志文件
3. 验证数据库连接

### 第四步: 网络连通性测试
1. 检查服务器间网络连接
2. 验证HTTPS证书
3. 测试跨域设置

## 修复建议

### 1. 立即修复
- ✅ 已修复app.json中的share配置错误
- ✅ 已移除本地API中不必要的Gemini配置
- ✅ 已改进错误处理和日志记录
- ✅ 已创建测试页面

### 2. API配额管理
```php
// 在jianli.php中添加请求限制
$last_request_time = time();
if (isset($_SESSION['last_api_call'])) {
    $time_diff = $last_request_time - $_SESSION['last_api_call'];
    if ($time_diff < 10) { // 10秒间隔
        http_response_code(429);
        echo json_encode(['error' => '请求过于频繁，请稍后再试']);
        exit;
    }
}
$_SESSION['last_api_call'] = $last_request_time;
```

### 3. 备用方案
如果Gemini API持续出现问题，可以考虑：
1. 使用其他AI服务（如OpenAI、Claude等）
2. 实现本地文本分析
3. 添加缓存机制减少API调用

## 测试流程

### 使用测试页面
1. **中转API测试**: 
   - 打开 `jianli_test.html`
   - 上传小文件（如图片格式简历）
   - 观察返回结果

2. **本地API测试**:
   - 打开 `简历辅助审查助手/api/test.html`
   - 测试完整上传流程
   - 检查日志文件

### 手动测试命令
```bash
# 测试中转API连通性
curl -X POST https://www.furrywoo.com/jianli/jianli.php

# 测试本地API
curl -X POST https://jianli.alidog.cn/api/upload.php
```

## 日志检查

### 本地API日志
位置: `简历辅助审查助手/api/logs/`
```bash
tail -f logs/$(date +%Y-%m-%d).log
```

### 中转API日志
需要在服务器上检查PHP错误日志:
```bash
tail -f /var/log/php_errors.log
```

## 常见错误码

| 错误码 | 含义 | 解决方案 |
|--------|------|----------|
| 429 | 请求频率限制 | 等待或增加配额 |
| 401 | API密钥无效 | 检查密钥配置 |
| 403 | 访问被拒绝 | 检查权限设置 |
| 500 | 服务器错误 | 检查服务器配置 |
| 404 | 文件不存在 | 检查文件路径 |

## 下一步行动

1. **立即执行**:
   - 将修复后的文件部署到服务器
   - 使用测试页面验证功能
   - 检查API配额状态

2. **监控**:
   - 设置API调用监控
   - 记录错误频率
   - 监控响应时间

3. **优化**:
   - 实现请求缓存
   - 添加重试机制
   - 优化错误处理

## 联系支持

如果问题持续存在，请提供以下信息：
1. 具体错误信息
2. 测试页面的返回结果
3. 服务器日志文件
4. API调用时间和频率
